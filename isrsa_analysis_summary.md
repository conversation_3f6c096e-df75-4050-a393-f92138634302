# IS-RSA 显著性分析报告
## Intersubject Representational Similarity Analysis (交叉主体表征相似性分析)

### 分析概述
本分析比较了人人（Human-Human）和人机（Human-AI）交互中的相似度分布，使用IS-RSA方法评估两种交互模式在表征相似性方面的显著差异。

### 分析方法
- **统计检验**: 独立样本t检验、Mann-Whitney U检验、Kolmogorov-Smirnov检验、Levene方差齐性检验
- **效应量**: <PERSON>'s d
- **显著性水平**: α = 0.05

---

## 任务一：冷门绝学 (Niche Academic Disciplines)

### 数据概况
- **人人交互**: 5,356 个相似度对
- **人机交互**: 4,186 个相似度对

### 描述性统计
| 指标 | 人人交互 | 人机交互 |
|------|----------|----------|
| 均值 | 0.7638 | 0.8349 |
| 标准差 | 0.0690 | 0.0557 |
| 中位数 | 0.7643 | 0.8352 |
| 最小值 | 0.5383 | 0.6503 |
| 最大值 | 1.0000 | 0.9606 |

### 效应量
- **<PERSON>'s d**: -1.1193 (大效应)
- **解释**: 人机交互的相似度显著高于人人交互，效应量很大

### 显著性检验结果
| 检验方法 | 统计量 | p值 | 显著性 |
|----------|--------|-----|--------|
| Levene方差齐性检验 | F=169.46 | p<0.001 | 方差不齐 |
| 独立样本t检验 | t=-55.67 | p<0.001 | **显著** |
| Mann-Whitney U检验 | U=4,736,736 | p<0.001 | **显著** |
| Kolmogorov-Smirnov检验 | D=0.4401 | p<0.001 | **显著** |

---

## 任务二：安全特工 (Security Agent)

### 数据概况
- **人人交互**: 4,371 个相似度对
- **人机交互**: 5,050 个相似度对

### 描述性统计
| 指标 | 人人交互 | 人机交互 |
|------|----------|----------|
| 均值 | 0.7890 | 0.8386 |
| 标准差 | 0.0646 | 0.0600 |
| 中位数 | 0.7892 | 0.8398 |
| 最小值 | 0.5781 | 0.6383 |
| 最大值 | 1.0000 | 0.9529 |

### 效应量
- **Cohen's d**: -0.7977 (中等效应)
- **解释**: 人机交互的相似度显著高于人人交互，效应量中等

### 显著性检验结果
| 检验方法 | 统计量 | p值 | 显著性 |
|----------|--------|-----|--------|
| Levene方差齐性检验 | F=16.35 | p<0.001 | 方差不齐 |
| 独立样本t检验 | t=-38.41 | p<0.001 | **显著** |
| Mann-Whitney U检验 | U=6,313,349 | p<0.001 | **显著** |
| Kolmogorov-Smirnov检验 | D=0.3173 | p<0.001 | **显著** |

---

## 主要发现

### 1. 显著性差异
- **两个任务都显示出显著差异**: 所有统计检验（t检验、Mann-Whitney U检验、K-S检验）的p值都小于0.001，表明人人和人机交互的相似度分布存在极显著差异。

### 2. 效应量分析
- **冷门绝学任务**: Cohen's d = -1.12（大效应）
- **安全特工任务**: Cohen's d = -0.80（中等效应）
- **共同特征**: 人机交互的相似度均显著高于人人交互

### 3. 分布特征
- **人机交互**: 相似度更高、变异性更小、分布更集中
- **人人交互**: 相似度相对较低、变异性更大、分布更分散

### 4. 任务差异
- **冷门绝学任务**的效应量更大，表明在这个任务中人人和人机的差异更为明显
- **安全特工任务**的效应量中等，但仍然显著

---

## IS-RSA 解释

### 理论意义
IS-RSA分析揭示了人人和人机交互在表征相似性结构上的根本差异：

1. **认知一致性**: 人机交互显示出更高的相似度，可能反映了AI系统的一致性和可预测性
2. **创造性多样性**: 人人交互的较低相似度可能反映了人类思维的多样性和创造性
3. **任务敏感性**: 不同任务类型对人人和人机差异的敏感性不同

### 实践意义
1. **教育应用**: 理解人机协作中的相似性模式有助于优化教学策略
2. **系统设计**: 可以指导AI系统的设计以更好地模拟人类思维的多样性
3. **评估标准**: 为人机交互质量评估提供量化指标

---

## 结论

通过IS-RSA分析，我们发现：

1. **显著差异存在**: 人人和人机交互在相似度分布上存在极显著差异（p<0.001）
2. **人机相似度更高**: 在两个任务中，人机交互都表现出更高的相似度和更小的变异性
3. **效应量可观**: 冷门绝学任务显示大效应（d=-1.12），安全特工任务显示中等效应（d=-0.80）
4. **分布模式不同**: K-S检验显示两组数据来自不同的分布

这些发现为理解人机交互的认知机制提供了重要的量化证据，并为相关研究和应用提供了科学依据。

---

*分析时间: 2025-09-22*  
*分析方法: Intersubject Representational Similarity Analysis (IS-RSA)*  
*统计软件: Python (scipy, numpy)*
