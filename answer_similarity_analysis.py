import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
import jieba
import jieba.analyse
from wordcloud import WordCloud
import openai
import json
import re
from typing import List, Dict, Tuple
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class AnswerSimilarityAnalyzer:
    """答案相似度分析器"""

    def __init__(self, csv_path: str, openai_api_key: str = None):
        """
        初始化分析器

        Args:
            csv_path: CSV文件路径
            openai_api_key: OpenAI API密钥
        """
        self.csv_path = csv_path
        self.openai_api_key = openai_api_key
        self.df = None
        self.answers = []
        self.answer_embeddings = None
        self.similarity_matrix = None

        if openai_api_key:
            openai.api_key = openai_api_key

    def load_and_preprocess_data(self) -> List[str]:
        """
        加载和预处理CSV数据

        Returns:
            处理后的答案列表
        """
        print("正在加载CSV数据...")

        # 读取CSV文件
        self.df = pd.read_csv(self.csv_path, encoding='utf-8')
        print(f"数据形状: {self.df.shape}")

        # 检查ans_content列
        if 'ans_content' not in self.df.columns:
            raise ValueError("CSV文件中未找到'ans_content'列")

        # 处理ans_content列，合并多行内容
        answers = []
        current_answer = ""
        current_id = None

        for idx, row in self.df.iterrows():
            # 获取当前行的ID和答案内容
            row_id = row.get('id', idx)
            ans_content = str(row['ans_content']).strip()

            # 如果ans_content不为空且不是NaN
            if ans_content and ans_content != 'nan':
                if current_id is None:
                    current_id = row_id
                    current_answer = ans_content
                elif current_id == row_id:
                    # 同一个ID，合并内容
                    current_answer += "\n" + ans_content
                else:
                    # 新的ID，保存前一个答案
                    if current_answer.strip():
                        answers.append({
                            'id': current_id,
                            'content': current_answer.strip(),
                            'metadata': {
                                'role': self.df[self.df['id'] == current_id]['Role'].iloc[0] if 'Role' in self.df.columns else None,
                                'group': self.df[self.df['id'] == current_id]['Group'].iloc[0] if 'Group' in self.df.columns else None
                            }
                        })
                    current_id = row_id
                    current_answer = ans_content

        # 添加最后一个答案
        if current_answer.strip():
            answers.append({
                'id': current_id,
                'content': current_answer.strip(),
                'metadata': {
                    'role': self.df[self.df['id'] == current_id]['Role'].iloc[0] if 'Role' in self.df.columns else None,
                    'group': self.df[self.df['id'] == current_id]['Group'].iloc[0] if 'Group' in self.df.columns else None
                }
            })

        self.answers = answers
        print(f"成功提取 {len(answers)} 个答案")

        # 显示前几个答案的预览
        print("\n答案预览:")
        for i, answer in enumerate(answers[:3]):
            print(f"答案 {i+1} (ID: {answer['id']}):")
            print(f"内容: {answer['content'][:100]}...")
            print(f"元数据: {answer['metadata']}")
            print("-" * 50)

        return [answer['content'] for answer in answers]

    def clean_text(self, text: str) -> str:
        """
        清理文本内容

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符但保留中文标点
        text = re.sub(r'[^\u4e00-\u9fff\w\s，。！？；：""''（）【】]', '', text)
        return text.strip()

    def segment_text(self, text: str) -> List[str]:
        """
        中文分词

        Args:
            text: 输入文本

        Returns:
            分词结果列表
        """
        # 清理文本
        cleaned_text = self.clean_text(text)

        # 使用jieba分词
        words = jieba.lcut(cleaned_text)

        # 过滤停用词和短词
        stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
        filtered_words = [word for word in words if len(word) > 1 and word not in stop_words]

        return filtered_words

    def get_bert_embeddings(self, texts: List[str]) -> np.ndarray:
        """
        使用BERT模型获取文本向量

        Args:
            texts: 文本列表

        Returns:
            BERT向量矩阵
        """
        try:
            from transformers import AutoTokenizer, AutoModel
            import torch

            print("正在加载BERT模型...")
            # 使用中文BERT模型
            model_name = "bert-base-chinese"
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModel.from_pretrained(model_name)

            embeddings = []
            batch_size = 8  # 批处理大小

            print(f"正在处理 {len(texts)} 个文本...")
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i+batch_size]

                # 分词和编码
                inputs = tokenizer(batch_texts,
                                 padding=True,
                                 truncation=True,
                                 max_length=512,
                                 return_tensors="pt")

                # 获取模型输出
                with torch.no_grad():
                    outputs = model(**inputs)
                    # 使用[CLS]标记的向量作为句子表示
                    batch_embeddings = outputs.last_hidden_state[:, 0, :].numpy()
                    embeddings.extend(batch_embeddings)

                print(f"已处理 {min(i+batch_size, len(texts))}/{len(texts)} 个文本")

            self.answer_embeddings = np.array(embeddings)
            print(f"BERT向量化完成，向量维度: {self.answer_embeddings.shape}")

            return self.answer_embeddings

        except ImportError:
            print("未安装transformers库，使用TF-IDF作为替代方案...")
            return self.get_tfidf_embeddings(texts)

    def get_tfidf_embeddings(self, texts: List[str]) -> np.ndarray:
        """
        使用TF-IDF获取文本向量（BERT的替代方案）

        Args:
            texts: 文本列表

        Returns:
            TF-IDF向量矩阵
        """
        print("正在使用TF-IDF进行向量化...")

        # 对文本进行分词
        segmented_texts = []
        for text in texts:
            words = self.segment_text(text)
            segmented_texts.append(' '.join(words))

        # 使用TF-IDF向量化
        vectorizer = TfidfVectorizer(max_features=1000, ngram_range=(1, 2))
        tfidf_matrix = vectorizer.fit_transform(segmented_texts)

        self.answer_embeddings = tfidf_matrix.toarray()
        print(f"TF-IDF向量化完成，向量维度: {self.answer_embeddings.shape}")

        return self.answer_embeddings

    def calculate_similarity_matrix(self) -> np.ndarray:
        """
        计算相似度矩阵

        Returns:
            相似度矩阵
        """
        if self.answer_embeddings is None:
            raise ValueError("请先进行向量化")

        print("正在计算相似度矩阵...")
        self.similarity_matrix = cosine_similarity(self.answer_embeddings)
        print(f"相似度矩阵计算完成，形状: {self.similarity_matrix.shape}")

        return self.similarity_matrix

    def visualize_similarity_matrix(self, save_path: str = "similarity_matrix.png"):
        """
        可视化相似度矩阵

        Args:
            save_path: 保存路径
        """
        if self.similarity_matrix is None:
            raise ValueError("请先计算相似度矩阵")

        plt.figure(figsize=(12, 10))

        # 创建热力图
        mask = np.triu(np.ones_like(self.similarity_matrix, dtype=bool))  # 只显示下三角
        sns.heatmap(self.similarity_matrix,
                   mask=mask,
                   annot=False,
                   cmap='coolwarm',
                   center=0,
                   square=True,
                   fmt='.2f',
                   cbar_kws={"shrink": .8})

        plt.title('答案相似度矩阵', fontsize=16, fontweight='bold')
        plt.xlabel('答案索引', fontsize=12)
        plt.ylabel('答案索引', fontsize=12)
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图形以释放内存

        print(f"相似度矩阵可视化已保存到: {save_path}")

    def visualize_similarity_violin(self, save_path: str = "similarity_violin.png", max_answers: int = 20):
        """
        使用小提琴图可视化每个答案与其他答案的相似度分布

        Args:
            save_path: 保存路径
            max_answers: 最大显示答案数量（避免图表过于拥挤）
        """
        if self.similarity_matrix is None:
            raise ValueError("请先计算相似度矩阵")

        print(f"正在生成小提琴图，显示前{min(max_answers, len(self.answers))}个答案的相似度分布...")

        # 准备数据
        violin_data = []
        answer_labels = []

        # 限制显示的答案数量
        n_answers = min(max_answers, len(self.answers))

        for i in range(n_answers):
            # 获取第i个答案与所有其他答案的相似度（排除自己）
            similarities = []
            for j in range(len(self.answers)):
                if i != j:
                    similarities.append(self.similarity_matrix[i, j])

            violin_data.extend(similarities)
            answer_labels.extend([f"ID_{self.answers[i]['id']}" for _ in similarities])

        # 创建DataFrame用于绘图
        import pandas as pd
        df_violin = pd.DataFrame({
            'Answer_ID': answer_labels,
            'Similarity': violin_data
        })

        # 创建小提琴图
        plt.figure(figsize=(15, 8))

        # 使用seaborn绘制小提琴图
        sns.violinplot(data=df_violin, x='Answer_ID', y='Similarity',
                      palette='viridis', inner='box')

        plt.title('各答案与其他答案的相似度分布（小提琴图）', fontsize=16, fontweight='bold')
        plt.xlabel('答案ID', fontsize=12)
        plt.ylabel('相似度', fontsize=12)
        plt.xticks(rotation=45, ha='right')

        # 添加网格线
        plt.grid(True, alpha=0.3, axis='y')

        # 添加统计信息
        plt.axhline(y=0.8, color='red', linestyle='--', alpha=0.7, label='高相似度阈值(0.8)')
        plt.axhline(y=0.5, color='orange', linestyle='--', alpha=0.7, label='中等相似度阈值(0.5)')
        plt.legend()

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"小提琴图已保存到: {save_path}")

        # 输出统计摘要
        print("\n相似度分布统计摘要:")
        for i in range(n_answers):
            answer_id = self.answers[i]['id']
            similarities = [self.similarity_matrix[i, j] for j in range(len(self.answers)) if i != j]

            print(f"ID_{answer_id}: 平均={np.mean(similarities):.3f}, "
                  f"中位数={np.median(similarities):.3f}, "
                  f"标准差={np.std(similarities):.3f}, "
                  f"最大={np.max(similarities):.3f}")

    def visualize_similarity_boxplot(self, save_path: str = "similarity_boxplot.png", max_answers: int = 20):
        """
        使用箱线图可视化每个答案与其他答案的相似度分布

        Args:
            save_path: 保存路径
            max_answers: 最大显示答案数量
        """
        if self.similarity_matrix is None:
            raise ValueError("请先计算相似度矩阵")

        print(f"正在生成箱线图，显示前{min(max_answers, len(self.answers))}个答案的相似度分布...")

        # 准备数据
        similarity_data = []
        answer_ids = []

        # 限制显示的答案数量
        n_answers = min(max_answers, len(self.answers))

        for i in range(n_answers):
            # 获取第i个答案与所有其他答案的相似度（排除自己）
            similarities = [self.similarity_matrix[i, j] for j in range(len(self.answers)) if i != j]
            similarity_data.append(similarities)
            answer_ids.append(f"ID_{self.answers[i]['id']}")

        # 创建箱线图
        plt.figure(figsize=(15, 8))

        box_plot = plt.boxplot(similarity_data, labels=answer_ids, patch_artist=True)

        # 美化箱线图
        colors = plt.cm.viridis(np.linspace(0, 1, len(similarity_data)))
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        plt.title('各答案与其他答案的相似度分布（箱线图）', fontsize=16, fontweight='bold')
        plt.xlabel('答案ID', fontsize=12)
        plt.ylabel('相似度', fontsize=12)
        plt.xticks(rotation=45, ha='right')

        # 添加网格线和阈值线
        plt.grid(True, alpha=0.3, axis='y')
        plt.axhline(y=0.8, color='red', linestyle='--', alpha=0.7, label='高相似度阈值(0.8)')
        plt.axhline(y=0.5, color='orange', linestyle='--', alpha=0.7, label='中等相似度阈值(0.5)')
        plt.legend()

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"箱线图已保存到: {save_path}")

    def find_most_similar_pairs(self, top_k: int = 10) -> List[Tuple]:
        """
        找出最相似的答案对

        Args:
            top_k: 返回前k个最相似的对

        Returns:
            相似度最高的答案对列表
        """
        if self.similarity_matrix is None:
            raise ValueError("请先计算相似度矩阵")

        # 获取上三角矩阵的索引和值（排除对角线）
        triu_indices = np.triu_indices_from(self.similarity_matrix, k=1)
        similarities = self.similarity_matrix[triu_indices]

        # 获取最相似的对
        top_indices = np.argsort(similarities)[-top_k:][::-1]

        similar_pairs = []
        for idx in top_indices:
            i, j = triu_indices[0][idx], triu_indices[1][idx]
            similarity = similarities[idx]

            similar_pairs.append({
                'answer1_idx': int(i),
                'answer2_idx': int(j),
                'answer1_id': self.answers[i]['id'],
                'answer2_id': self.answers[j]['id'],
                'similarity': float(similarity),
                'answer1_content': self.answers[i]['content'][:100] + "...",
                'answer2_content': self.answers[j]['content'][:100] + "..."
            })

        return similar_pairs

    def analyze_themes_with_openai(self, sample_size: int = 20) -> Dict:
        """
        使用OpenAI进行主题分析

        Args:
            sample_size: 分析的样本数量

        Returns:
            主题分析结果
        """
        if not self.openai_api_key:
            print("未提供OpenAI API密钥，跳过主题分析")
            return {}

        # 选择样本进行分析
        sample_answers = self.answers[:sample_size] if len(self.answers) > sample_size else self.answers
        combined_text = "\n\n".join([f"答案{i+1}: {answer['content']}" for i, answer in enumerate(sample_answers)])

        try:
            print(f"正在使用OpenAI分析 {len(sample_answers)} 个答案的主题...")

            prompt = f"""
            请分析以下答案的主要主题和内容特点：

            {combined_text}

            请提供以下分析：
            1. 主要主题（3-5个）
            2. 关键词汇（10-15个）
            3. 答案的整体特点
            4. 内容分类建议

            请用JSON格式返回结果。
            """

            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是一个专业的文本分析专家，擅长主题提取和内容分析。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.3
            )

            result = response.choices[0].message.content

            try:
                # 尝试解析JSON
                theme_analysis = json.loads(result)
            except json.JSONDecodeError:
                # 如果不是有效JSON，返回原始文本
                theme_analysis = {"raw_analysis": result}

            print("OpenAI主题分析完成")
            return theme_analysis

        except Exception as e:
            print(f"OpenAI主题分析出错: {e}")
            return {"error": str(e)}

    def generate_wordcloud(self, save_path: str = "wordcloud.png", max_words: int = 100):
        """
        生成词云

        Args:
            save_path: 保存路径
            max_words: 最大词数
        """
        print("正在生成词云...")

        # 合并所有答案文本
        all_text = " ".join([answer['content'] for answer in self.answers])

        # 分词
        words = self.segment_text(all_text)
        text_for_wordcloud = " ".join(words)

        try:
            # 创建词云
            wordcloud = WordCloud(
                font_path='simhei.ttf',  # 中文字体路径，可能需要调整
                width=800,
                height=600,
                background_color='white',
                max_words=max_words,
                colormap='viridis',
                relative_scaling=0.5,
                random_state=42
            ).generate(text_for_wordcloud)

            # 显示词云
            plt.figure(figsize=(12, 8))
            plt.imshow(wordcloud, interpolation='bilinear')
            plt.axis('off')
            plt.title('答案内容词云', fontsize=16, fontweight='bold')
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()  # 关闭图形以释放内存

            print(f"词云已保存到: {save_path}")

        except Exception as e:
            print(f"词云生成出错: {e}")
            # 使用matplotlib生成简单的词频图作为替代
            self.generate_word_frequency_chart(words, save_path.replace('.png', '_freq.png'))

    def generate_word_frequency_chart(self, words: List[str], save_path: str = "word_frequency.png"):
        """
        生成词频图表

        Args:
            words: 词汇列表
            save_path: 保存路径
        """
        from collections import Counter

        # 统计词频
        word_freq = Counter(words)
        top_words = word_freq.most_common(20)

        # 创建条形图
        words_list, freqs = zip(*top_words)

        plt.figure(figsize=(12, 8))
        bars = plt.barh(range(len(words_list)), freqs)
        plt.yticks(range(len(words_list)), words_list)
        plt.xlabel('频次', fontsize=12)
        plt.title('高频词汇统计', fontsize=16, fontweight='bold')
        plt.gca().invert_yaxis()

        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            plt.text(width + 0.1, bar.get_y() + bar.get_height()/2,
                    str(freqs[i]), ha='left', va='center')

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图形以释放内存

        print(f"词频图表已保存到: {save_path}")

    def generate_comprehensive_report(self, output_path: str = "similarity_analysis_report.json"):
        """
        生成综合分析报告

        Args:
            output_path: 输出路径
        """
        print("正在生成综合分析报告...")

        # 基本统计信息
        lengths = [len(answer['content']) for answer in self.answers]
        basic_stats = {
            'total_answers': len(self.answers),
            'average_length': float(np.mean(lengths)),
            'min_length': int(min(lengths)),
            'max_length': int(max(lengths))
        }

        # 相似度统计
        similarity_stats = {}
        if self.similarity_matrix is not None:
            # 排除对角线元素
            triu_indices = np.triu_indices_from(self.similarity_matrix, k=1)
            similarities = self.similarity_matrix[triu_indices]

            similarity_stats = {
                'mean_similarity': float(np.mean(similarities)),
                'std_similarity': float(np.std(similarities)),
                'min_similarity': float(np.min(similarities)),
                'max_similarity': float(np.max(similarities)),
                'high_similarity_pairs': int(np.sum(similarities > 0.8)),
                'medium_similarity_pairs': int(np.sum((similarities > 0.5) & (similarities <= 0.8))),
                'low_similarity_pairs': int(np.sum(similarities <= 0.5))
            }

        # 最相似的答案对
        most_similar = self.find_most_similar_pairs(top_k=5) if self.similarity_matrix is not None else []

        # OpenAI主题分析
        theme_analysis = self.analyze_themes_with_openai()

        # 生成报告
        report = {
            'analysis_timestamp': pd.Timestamp.now().isoformat(),
            'basic_statistics': basic_stats,
            'similarity_statistics': similarity_stats,
            'most_similar_pairs': most_similar,
            'theme_analysis': theme_analysis,
            'methodology': {
                'vectorization': 'BERT (bert-base-chinese) or TF-IDF as fallback',
                'similarity_metric': 'Cosine Similarity',
                'text_preprocessing': 'Chinese word segmentation with jieba'
            }
        }

        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"综合分析报告已保存到: {output_path}")
        return report

def main():
    """主函数 - 完整的分析流程"""
    print("=" * 60)
    print("答案相似度分析系统")
    print("=" * 60)

    # 初始化分析器
    # 如果有OpenAI API密钥，请在这里设置
    openai_api_key = None  # 请设置您的OpenAI API密钥
    analyzer = AnswerSimilarityAnalyzer("all_info_with_cover_metrics_human.csv_1.csv", openai_api_key)

    try:
        # 步骤1: 数据预处理
        print("\n步骤1: 数据预处理")
        answer_texts = analyzer.load_and_preprocess_data()

        if len(answer_texts) == 0:
            print("未找到有效的答案数据")
            return

        # 步骤2: BERT向量化
        print("\n步骤2: 文本向量化")
        embeddings = analyzer.get_bert_embeddings(answer_texts)

        # 步骤3: 计算相似度矩阵
        print("\n步骤3: 计算相似度矩阵")
        similarity_matrix = analyzer.calculate_similarity_matrix()

        # 步骤4: 可视化相似度分布
        print("\n步骤4: 生成相似度分布可视化")
        analyzer.visualize_similarity_violin("similarity_violin.png", max_answers=150)
        analyzer.visualize_similarity_boxplot("similarity_boxplot.png", max_answers=150)

        # 步骤5: 找出最相似的答案对
        print("\n步骤5: 分析最相似的答案对")
        similar_pairs = analyzer.find_most_similar_pairs(top_k=10)

        print("\n最相似的答案对:")
        for i, pair in enumerate(similar_pairs[:5], 1):
            print(f"\n{i}. 相似度: {pair['similarity']:.3f}")
            print(f"   答案1 (ID: {pair['answer1_id']}): {pair['answer1_content']}")
            print(f"   答案2 (ID: {pair['answer2_id']}): {pair['answer2_content']}")

        # 步骤6: 生成词云
        print("\n步骤6: 生成词云")
        analyzer.generate_wordcloud()

        # 步骤7: OpenAI主题分析（如果提供了API密钥）
        if openai_api_key:
            print("\n步骤7: OpenAI主题分析")
            theme_analysis = analyzer.analyze_themes_with_openai()
            print("主题分析结果:")
            print(json.dumps(theme_analysis, ensure_ascii=False, indent=2))
        else:
            print("\n步骤7: 跳过OpenAI主题分析（未提供API密钥）")

        # 步骤8: 生成综合报告
        print("\n步骤8: 生成综合分析报告")
        report = analyzer.generate_comprehensive_report()

        print("\n" + "=" * 60)
        print("分析完成！生成的文件:")
        print("- similarity_violin.png: 相似度分布小提琴图")
        print("- similarity_boxplot.png: 相似度分布箱线图")
        print("- wordcloud.png: 词云图")
        print("- similarity_analysis_report.json: 综合分析报告")
        print("- processed_answers.json: 预处理后的答案数据")
        print("=" * 60)

        # 显示基本统计信息
        print(f"\n基本统计信息:")
        print(f"- 总答案数: {report['basic_statistics']['total_answers']}")
        print(f"- 平均长度: {report['basic_statistics']['average_length']:.1f} 字符")
        if 'similarity_statistics' in report and report['similarity_statistics']:
            print(f"- 平均相似度: {report['similarity_statistics'].get('mean_similarity', 'N/A'):.3f}")
            print(f"- 高相似度对数 (>0.8): {report['similarity_statistics'].get('high_similarity_pairs', 'N/A')}")

    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()