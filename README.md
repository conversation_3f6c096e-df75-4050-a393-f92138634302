# 答案相似度分析系统

这是一个功能完整的Python系统，用于分析CSV文件中答案内容的相似度。系统支持BERT向量化、相似度计算、OpenAI主题分析和词云生成等功能。

## 🚀 功能特性

- **智能数据预处理**: 自动解析CSV文件，智能合并多行答案内容
- **BERT向量化**: 使用中文BERT模型(`bert-base-chinese`)进行高质量文本向量化
- **TF-IDF备选方案**: 当BERT不可用时自动切换到TF-IDF向量化
- **相似度分析**: 计算答案间的余弦相似度，生成完整相似度矩阵
- **可视化分析**: 生成相似度矩阵热力图和高频词汇统计图
- **词云生成**: 创建美观的词云图展示关键词汇
- **OpenAI主题分析**: 调用GPT模型进行智能主题提取和内容分析
- **综合报告**: 生成详细的JSON格式分析报告

## 📋 系统要求

- Python 3.7+
- 至少4GB内存（用于BERT模型）
- 网络连接（首次下载BERT模型）

## 🔧 安装步骤

### 1. 克隆或下载代码
```bash
# 确保您有以下文件：
# - answer_similarity_analysis.py
# - requirements.txt
# - all_info_with_cover_metrics_ai.csv (您的数据文件)
```

### 2. 安装依赖包
```bash
pip install -r requirements.txt
```

### 3. 安装中文字体（可选，用于词云）
- Windows: 系统自带SimHei字体
- Linux/Mac: 可能需要安装中文字体包

## 📊 CSV文件格式要求

您的CSV文件必须包含以下列：

| 列名 | 必需 | 说明 |
|------|------|------|
| `ans_content` | ✅ | 答案内容，支持多行 |
| `id` | 推荐 | 答案唯一标识符 |
| `Role` | 可选 | 角色信息（如：协调员、专家等） |
| `Group` | 可选 | 分组信息 |

### CSV示例：
```csv
id,Role,Group,ans_content
101001,协调员,1,"这是第一个答案的内容..."
101001,协调员,1,"这是同一答案的续行内容..."
101002,专家,1,"这是第二个答案的内容..."
```

## 🎯 使用方法

### 方法1: 直接运行完整分析
```bash
python answer_similarity_analysis.py
```

这将执行完整的分析流程，包括：
1. 数据预处理
2. BERT向量化
3. 相似度计算
4. 可视化生成
5. 词云创建
6. 综合报告生成

### 方法2: 分步骤使用
```python
from answer_similarity_analysis import AnswerSimilarityAnalyzer

# 初始化分析器
analyzer = AnswerSimilarityAnalyzer("all_info_with_cover_metrics_ai.csv")

# 步骤1: 加载和预处理数据
answer_texts = analyzer.load_and_preprocess_data()
print(f"成功加载 {len(answer_texts)} 个答案")

# 步骤2: 向量化（自动选择BERT或TF-IDF）
embeddings = analyzer.get_bert_embeddings(answer_texts)

# 步骤3: 计算相似度矩阵
similarity_matrix = analyzer.calculate_similarity_matrix()

# 步骤4: 生成可视化
analyzer.visualize_similarity_matrix("my_similarity_matrix.png")

# 步骤5: 找出最相似的答案对
similar_pairs = analyzer.find_most_similar_pairs(top_k=10)
for pair in similar_pairs[:3]:
    print(f"相似度: {pair['similarity']:.3f}")
    print(f"答案1: {pair['answer1_content']}")
    print(f"答案2: {pair['answer2_content']}")
    print("-" * 50)

# 步骤6: 生成词云
analyzer.generate_wordcloud("my_wordcloud.png")

# 步骤7: 生成综合报告
report = analyzer.generate_comprehensive_report("my_report.json")
```

### 方法3: 使用OpenAI主题分析
```python
# 设置OpenAI API密钥
openai_api_key = "sk-your-api-key-here"
analyzer = AnswerSimilarityAnalyzer(
    "all_info_with_cover_metrics_ai.csv",
    openai_api_key=openai_api_key
)

# 运行完整分析（包括OpenAI主题分析）
# 然后运行 python answer_similarity_analysis.py
```

## 📁 输出文件说明

运行完成后，系统会生成以下文件：

| 文件名 | 说明 |
|--------|------|
| `similarity_matrix.png` | 相似度矩阵热力图，显示所有答案对之间的相似度 |
| `wordcloud.png` | 词云图，展示答案中的高频关键词 |
| `word_frequency.png` | 词频统计图（词云生成失败时的备选） |
| `similarity_analysis_report.json` | 详细的分析报告（JSON格式） |
| `processed_answers.json` | 预处理后的答案数据 |

## 📈 分析报告内容

生成的JSON报告包含：

```json
{
  "analysis_timestamp": "2024-01-01T12:00:00",
  "basic_statistics": {
    "total_answers": 150,
    "average_length": 245.3,
    "min_length": 50,
    "max_length": 800
  },
  "similarity_statistics": {
    "mean_similarity": 0.342,
    "std_similarity": 0.156,
    "high_similarity_pairs": 12,
    "medium_similarity_pairs": 45,
    "low_similarity_pairs": 93
  },
  "most_similar_pairs": [...],
  "theme_analysis": {...},
  "methodology": {...}
}
```

## 🔑 OpenAI API配置

### 获取API密钥
1. 访问 [OpenAI官网](https://platform.openai.com/)
2. 注册账户并获取API密钥
3. 确保账户有足够的API调用额度

### 配置方法
```python
# 方法1: 直接在代码中设置
openai_api_key = "sk-your-api-key-here"

# 方法2: 使用环境变量
import os
openai_api_key = os.getenv("OPENAI_API_KEY")

# 方法3: 修改main()函数中的openai_api_key变量
```

### OpenAI分析功能
- 自动提取主要主题（3-5个）
- 识别关键词汇（10-15个）
- 分析答案整体特点
- 提供内容分类建议

## 🛠️ 高级配置

### 自定义参数
```python
# 自定义相似度阈值
analyzer = AnswerSimilarityAnalyzer("data.csv")
high_similarity_pairs = analyzer.find_most_similar_pairs(top_k=20)

# 自定义词云参数
analyzer.generate_wordcloud(
    save_path="custom_wordcloud.png",
    max_words=200
)

# 自定义OpenAI分析样本数
theme_analysis = analyzer.analyze_themes_with_openai(sample_size=50)
```

### 批量处理大文件
```python
# 对于大量数据，可以分批处理
def process_large_dataset(csv_path, batch_size=100):
    analyzer = AnswerSimilarityAnalyzer(csv_path)
    answer_texts = analyzer.load_and_preprocess_data()

    # 分批处理
    for i in range(0, len(answer_texts), batch_size):
        batch = answer_texts[i:i+batch_size]
        batch_embeddings = analyzer.get_bert_embeddings(batch)
        # 处理批次结果...
```

## 📊 结果解读指南

### 相似度矩阵解读
- **颜色深浅**: 颜色越深表示相似度越高
- **对角线**: 始终为1.0（自己与自己的相似度）
- **对称性**: 矩阵关于对角线对称
- **相似度范围**: 0.0（完全不相似）到 1.0（完全相同）

### 相似度阈值建议
- **高相似度** (>0.8): 内容基本相同或高度重复
- **中等相似度** (0.5-0.8): 主题相关，表达方式不同
- **低相似度** (<0.5): 内容差异较大

### 词云解读
- **字体大小**: 反映词汇出现频率
- **颜色**: 纯装饰性，无特殊含义
- **位置**: 随机分布，无特殊含义

## 🔍 故障排除

### 常见问题及解决方案

#### 1. BERT模型下载失败
```bash
# 问题：网络连接问题或防火墙阻止
# 解决：
# 方案1: 使用代理
export HTTP_PROXY=http://your-proxy:port
export HTTPS_PROXY=http://your-proxy:port

# 方案2: 手动下载模型到本地
# 从 https://huggingface.co/bert-base-chinese 下载模型文件
```

#### 2. 内存不足错误
```python
# 问题：处理大量数据时内存不足
# 解决：减少批处理大小
def get_bert_embeddings_small_batch(self, texts, batch_size=4):
    # 在代码中修改batch_size参数
```

#### 3. 词云生成失败
```bash
# 问题：缺少中文字体
# 解决：
# Windows: 确保有SimHei字体
# Linux: sudo apt-get install fonts-wqy-zenhei
# Mac: brew install font-wqy-zenhei
```

#### 4. OpenAI API调用失败
```python
# 问题：API密钥无效或额度不足
# 解决：
# 1. 检查API密钥格式：sk-...
# 2. 检查账户余额
# 3. 检查API调用限制
```

#### 5. CSV编码问题
```python
# 问题：中文字符显示乱码
# 解决：指定正确编码
df = pd.read_csv(csv_path, encoding='utf-8-sig')  # 或 'gbk'
```

## 📋 性能优化建议

### 1. 硬件优化
- **CPU**: 多核处理器有助于并行计算
- **内存**: 至少8GB，推荐16GB以上
- **存储**: SSD硬盘提升I/O性能

### 2. 软件优化
```python
# 使用GPU加速（如果可用）
import torch
if torch.cuda.is_available():
    device = torch.device("cuda")
    model = model.to(device)
```

### 3. 数据预处理优化
```python
# 预先过滤过短或过长的文本
def filter_texts(texts, min_length=10, max_length=1000):
    return [text for text in texts
            if min_length <= len(text) <= max_length]
```

## 📚 扩展功能建议

### 1. 添加更多相似度算法
```python
# 可以扩展支持其他相似度算法
from sklearn.metrics.pairwise import euclidean_distances
from scipy.spatial.distance import jaccard

def calculate_multiple_similarities(self):
    cosine_sim = cosine_similarity(self.answer_embeddings)
    euclidean_sim = euclidean_distances(self.answer_embeddings)
    return {"cosine": cosine_sim, "euclidean": euclidean_sim}
```

### 2. 添加聚类分析
```python
from sklearn.cluster import KMeans

def cluster_answers(self, n_clusters=5):
    kmeans = KMeans(n_clusters=n_clusters)
    clusters = kmeans.fit_predict(self.answer_embeddings)
    return clusters
```

### 3. 添加情感分析
```python
def analyze_sentiment(self, texts):
    # 可以集成情感分析库
    from textblob import TextBlob
    sentiments = [TextBlob(text).sentiment.polarity for text in texts]
    return sentiments
```

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **检查依赖**: 确保所有依赖包都已正确安装
2. **查看日志**: 注意控制台输出的错误信息
3. **检查数据**: 确认CSV文件格式正确
4. **内存监控**: 使用任务管理器监控内存使用情况

## 📄 许可证

MIT License - 您可以自由使用、修改和分发此代码。

## 🔄 版本更新日志

- **v1.0.0**: 初始版本，支持基本的相似度分析
- 支持BERT和TF-IDF向量化
- 支持OpenAI主题分析
- 支持词云生成和可视化

---

**祝您使用愉快！如有问题欢迎反馈。** 🎉