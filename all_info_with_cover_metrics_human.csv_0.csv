id,Group,Date_Group,Role,发散得分(allcount),发散得分（nowater）,创造力得分,创造力总分,extra,open,agree,consci,neuro,engasub2,engasub1,exchange,imagine,grit,curious,reflex,regulate,moniter,plan,sef,sys,ana,sdr,engacog2,engaemo2,engabeh2,engacog1,engaemo1,engabeh1,respon,conrel,conbeh,tsrel1,stra1,ethic1,thk1,tsrel,sta,ethic,thk,group,course,role,roleid,formid,taskid,firsttask,ptype,SE,SEC,CSR1,CSR2,CSR3,CSR4,CSR5,CSR6,ESR1,ESR2,ESR3,ESR4,ESR5,HOT1,HOT2,HOT3,HOT4,HOT5,HOT6,CCR1,CCR2,CCR3,CCR4,CCR5,ECR1,ECR2,ECR3,ECR4,ECR5,CCSR1,CCSR2,CCSR3,CCSR4,CCSR5,ECSR1,ECSR2,ECSR3,ECSR4,ECSR5,CPS1,CPS2,CPS3,CPS4,EPS1,EPS2,EPS3,EPS4,CLIL1,CLIL2,CLGL1,CLEL1,CLGL2,CLEL2,CLEL3,CLGL3,CLEL4,CLIL3,MF,ES1,ES2,SAM_Valence,SAM_Arousal,SAM_Dominance,Connection,SE_mean,SEC_mean,CSR_mean,ESR_mean,CCR_mean,ECR_mean,CCSR_mean,ECSR_mean,HOT_mean,CPS_mean,EPS_mean,CLIL_mean,CLGL_mean,CLEL_mean,SAM_Valence_mean,SAM_Arousal_mean,SAM_Dominance_mean,Connection_mean,TaskResponsiveness,Technicaltheorysupport,Innovation,Divergence,Problemsolvingskills,age,gender,mas11,test1,mcj1,test2,mcj2,test3,mcj3,test4,mcj4,test5,mcj5,test6,mcj6,test7,mcj7,test8,mcj8,test9,mcj9,test10,mcj10,test11,mcj11,test12,mcj12,mas12,beh11,beh12,beh13,emo11,emo12,emo13,cog11,cog12,cog13,cog14,cog15,hard1,sub11,sub12,sub13,mas21,test13,mcj13,test14,mcj14,test15,mcj15,test16,mcj16,test17,mcj17,test18,mcj18,test19,mcj19,test20,mcj20,test21,mcj21,test22,mcj22,mas22,beh21,beh22,beh23,emo21,emo22,emo23,cog21,cog22,cog23,cog24,cog25,hard2,sub21,sub22,sub23,pretest,score_mean,bias,score_mean_withoutbias,bias_withoutbias,TaskResponsiveness_adjusted,Technicaltheorysupport_adjusted,Innovation_adjusted,Divergence_adjusted,Problemsolvingskills_adjusted,ab_monitoring,人名,说话人编号,ans_content,bleu,rouge_rouge1,rouge_rouge2,rouge_rougeL,meteor,tfidf_cosine,bertscore_f1,lcs_ratio,edit_distance_similarity
101002,1,1001,协调员,5.0,1.0,3.0,3.0,4.0,4.0,2.6666666666666665,3.333333333333333,6.0,2.6666666666666665,3.0,4.0,4.333333333,4.0,4.333333333,3.433333333,3.6,3.6,3.6,3.1,3.5,3.7,0.375,3.8,3.333333333,2.333333333,3.0,3.333333333,2.0,3.5,3.666666667,3.5,3.2,3.0,3.6,4.2,16,12,18,21,1,A,1,2,0,0,1,1,4,9,3,1,4,4,4,4,3,5,5,5,5,4,5,5,5,5,4,5,5,5,5,2,5,5,5,5,5,3,5,5,5,5,5,5,5,5,5,5,5,3,5,5,3,5,5,4,5,5,1,5,5,3,5,1,2,3,5,5,3,3,4,2,7.125,9,3.333333333,4.6,4.4,5.0,4.6,5.0,4.666666667,4.5,4.5,3.666666667,5.0,2.5,3,3,4,2,6.0,4.5,5.5,5.5,5.0,19,0,3.0,0,2,1,1,0,2,1,1,0,1,1,1,0,1,0,2,0,2,0,2,1,2,0,2,3,3,2,1,2,4,4,2,4,3,4,2,3,3,4,2,3,1,1,0,2,0,1,0,1,0,1,1,1,0,1,0,2,0,1,1,1,2,2,3,2,3,3,4,4,3,4,4,4,3,4,2,2,0.318181818,5.3,-1.3,6.1855,-2.1855,6.827,5.805,6.238,6.331,5.725,2.1855,郑钰娜,2.0,"我认为阿塞拜疆语是一门很适应ai来学习的。首先，作为语言来说，阿塞拜疆语有着庞大的数据库，利于导入数据。同时阿塞拜疆语这两年，随着一带一路政策的深入，对这个语言的需求也在增大。但是因为这个语言相对冷门，如果要专门学习，需要大量精力，所以可以通过ai开发精确的翻译器，帮助准确的交流。同时因为数据库可以随时更新，阿塞拜疆语的也可以随时加入新的阿塞拜疆语的用法，便于创新。社会应用来说，也有助于一带一路活动的开展，便于两国人民交流，增加阿塞拜疆语的运用范围。
ai的举一反三能力也有助于精确理解这个语言，避免出现翻译错误",2.0491779584668012e-11,0.044776119402985,0.0303030303030303,0.044776119402985,0.0135730007336757,0.3125093916430612,0.1122075393795967,0.3846153846153846,0.0175904414205111
101011,1,1001,启发员,8.0,6.0,5.0,5.0,1.0,1.0,4.333333333333333,4.0,1.0,1.0,1.6666666666666667,5.0,5.0,5.333333333,6.0,3.140740741,4.844444444,4.066666667,2.4,5.0,4.8,5.6,0.625,5.0,4.0,3.333333333,4.2,3.666666667,3.0,5.0,4.333333333,4.5,4.0,3.5,3.6,5.0,20,14,18,25,1,A,1,1,0,0,1,1,7,5,5,5,5,5,3,1,4,3,5,4,4,5,5,5,5,5,4,3,4,4,2,5,3,3,3,3,3,3,3,4,5,5,3,3,3,5,5,4,4,4,4,4,4,3,3,1,1,5,1,4,1,1,4,1,1,1,4,4,4,2,4,3,5.75,5,4.0,4.0,3.6,3.0,4.0,3.8,4.833333333,4.0,3.5,1.0,4.333333333,1.0,4,2,4,3,6.0,4.5,5.0,5.5,5.5,24,1,10.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,10,3,3,3,2,4,5,4,5,4,4,4,1,3,1,1,10,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,10,3,3,4,4,4,4,5,5,5,5,5,1,1,1,1,0.818181818,5.3,1.7,6.1855,0.8145,6.827,5.805,5.738,6.331,6.225,0.8145,徐海龙,1.0,"对于油气储存运输行业，在几十年的发展中，已经积累了大量基于机理的知识模式与基于经验的行业知识。这类知识中包括大量对前瞻研究的启发性较弱、但对从业人员必须了解的知识，通过大模型的使用，将海量的知识数据（包括书籍、照片（如损伤识别）、老员工口述心得）融合进人工智能中，能够加快该领域学习者的学习效率，并能更快的跟进科研一线，了解网络难以查询到的知识。
此外，通过大模型的思维链去完善链式操作或操作逻辑，能够对复杂的实验过程或工艺操作过程进行实时监督，在新人进行操作时，能够更安全、高效得完成实验。
能源保障工作一直是国家重大要事，通过融合人工智能可以为能源行业进一步发展提供便携。",1.0123515125497294e-08,0.0,0.0,0.0,0.0176601404090175,0.2636115497085077,0.0944841876626014,0.313953488372093,0.0209568999604586
101012,1,1001,记录员,5.0,6.0,7.0,7.0,2.333333333333333,3.0,3.0,2.6666666666666665,4.333333333333333,2.0,2.333333333333333,4.0,2.666666667,3.0,3.666666667,2.576851852,3.461111111,2.766666667,2.6,3.0,3.4,3.3,0.125,3.0,3.666666667,3.333333333,3.4,4.0,2.333333333,4.0,3.666666667,4.0,3.0,3.0,3.0,3.0,15,12,15,15,1,A,1,3,0,0,1,1,8,8,3,3,4,4,4,4,4,4,4,4,4,4,3,3,3,4,3,4,2,3,2,2,2,2,2,4,3,3,3,3,3,3,3,3,3,3,3,4,3,2,3,3,3,3,3,3,4,4,4,4,4,4,4,4,3,3,4,4,3,2,4,2,8.0,8,3.666666667,4.0,2.6,2.6,3.0,3.0,3.333333333,3.0,3.0,3.333333333,4.0,4.0,3,2,4,2,6.0,6.5,5.5,6.0,6.5,20,1,6.0,1,1,1,1,1,1,1,1,1,2,1,1,0,1,0,1,1,1,1,1,1,1,0,2,6,3,2,2,4,4,4,4,3,4,4,2,3,3,2,2,6,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,2,1,1,6,3,4,3,4,4,3,3,3,3,3,3,3,2,2,2,0.818181818,6.1,1.9,6.9855,1.0145,6.827,7.805,6.238,6.831,7.225,1.0145,卢瑞涛,3.0,"考古学作为一个冷门学科，其专业知识有着大众认知广而认知浅，其学习需要消耗大量时间的特点。而古代遗留的物件，文字均遭受不同程度的磨损，可能难以识别。
而AI技术有助于考古学的复兴，主要集中于以下方面：AI的数据链技术可以呈现AI的思考过程，通过一步一步揭示文物，文字的特征反映出的信息，从而清晰可视地呈现出对于古文物的辨认过程，不仅有助于专业工作者的工作，也有利于大众对于古代文化的学习；AI的多模态技术通过图生文，文/图生视频等方式，不仅可以让文物”活起来“，还可以还原重大历史事件，有助于专业研究与文化传承；同时，利用AI获取与学习大量文物，考古学知识的能力，可以实现”AI考古学老师”授课，有效提高考古学教育水平。",2.706027652594524e-07,0.1212121212121212,0.1030927835051546,0.1212121212121212,0.0250336185077168,0.3087247108718657,0.2552287578582763,0.4606741573033708,0.0312862108922363
101005,2,1002,启发员,2.0,3.0,2.0,2.0,1.3333333333333333,2.0,5.666666666666667,2.0,2.0,4.0,3.6666666666666665,5.0,4.0,2.333333333,3.666666667,2.575925926,3.455555556,2.733333333,2.4,3.0,2.4,4.1,0.25,4.0,4.0,3.333333333,4.0,4.0,3.666666667,4.0,4.0,4.0,4.0,4.0,4.6,4.4,20,16,23,22,2,A,1,1,0,0,1,1,9,8,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,1,1,5,5,3,5,5,5,5,1,5,5,8.375,8,4.0,4.0,3.8,4.0,5.0,5.0,4.0,5.0,5.0,4.333333333,5.0,3.0,5,1,5,5,4.5,4.0,5.0,4.5,5.5,25,1,7.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,0,2,0,1,7,4,3,4,4,4,4,4,4,4,4,4,2,4,4,3,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,0,1,7,4,2,4,4,4,4,4,4,4,4,4,3,4,4,4,0.681818182,4.7,4.3,5.5855,3.4145,5.327,5.305,5.738,5.331,6.225,3.4145,赵昊博,2.0,"人工智能在石油与天然气工程领域的应用：
相较于其他工科领域，石油与天然气领域更为专业化、小众化，石油行业也是夕阳企业的代表，而人工智能在石油与天然气领域的应用，更偏向于对工程的仿真控制以及预测方面，石油与天然气行业身为老牌工科行业，积累了大量的运行数据，而这些数据的利用率却很低，可以通过人工智能的学习能力，将大量的运行数据运用起来，一是对现场工艺的仿真预测，现场由于其危险性，许多危险工况难以模拟，可以通过人工智能建立数据驱模型，对其他工况进行模拟，也可以将仿真预测结果输出至现场，提供现场控制方案，更可以总结事故发生规律，对高危区域进行预警。",5.340783583817161e-07,0.0,0.0,0.0,0.0247116968698517,0.3521122719430748,0.1452636867761612,0.3291139240506329,0.0293778801843318
101007,2,1002,记录员,3.0,4.0,3.0,4.0,5.0,4.0,5.333333333333333,4.0,2.0,4.333333333333333,4.0,5.0,4.333333333,5.0,5.333333333,3.731481481,3.388888889,3.333333333,4.0,3.6,4.2,3.9,0.5,4.2,4.666666667,4.0,4.2,3.666666667,3.666666667,4.0,4.0,4.25,4.2,4.25,4.0,4.4,21,17,20,22,2,A,1,3,0,0,1,1,8,9,4,5,4,4,4,5,4,4,4,4,3,4,4,4,3,4,4,4,4,4,4,3,4,4,4,4,4,5,4,4,4,5,4,4,4,4,4,4,4,4,4,4,5,3,4,5,3,4,4,4,3,4,3,5,3,5,4,4,4,2,3,6,8.625,9,4.333333333,3.8,3.8,4.0,4.4,4.0,3.833333333,4.0,4.0,3.666666667,3.666666667,4.0,4,2,3,6,3.5,2.5,4.0,4.0,4.5,29,1,7.0,1,1,1,1,1,1,0,2,1,2,1,1,0,1,0,1,1,1,0,1,0,2,1,1,6,4,3,4,4,4,3,4,4,4,4,5,3,4,5,3,7,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,2,0,2,1,1,7,4,4,4,5,5,4,4,4,5,4,4,3,5,4,4,0.727272727,3.7,4.3,4.5855,3.4145,4.327,3.805,4.738,4.831,5.225,3.4145,范志涵,3.0,"人工智能石油天然气领域的应用
铺设石油天然气管路时的管线路径规划和站场控制可以应用人工智能技术。
1 在铺设天然气管路时，人工智能可以辅助或者替代人根据地形，土质，人口密集程度进行管路设计；
2 石油天然气管路的站场经常涉及到很多控制阀的操作，这非常依赖人的经验。人工智能可以辅助设计控制阀的开关顺序和开关程度。",6.703711663253124e-08,0.064516129032258,0.0,0.064516129032258,0.0181035326352872,0.3380227295936477,0.0985511243343353,0.3411764705882353,0.0257548845470693
101016,2,1002,协调员,4.0,8.0,3.0,3.0,3.0,2.6666666666666665,3.0,3.0,3.0,3.6666666666666665,4.0,3.0,4.0,3.0,3.0,3.887037037,3.322222222,3.933333333,3.6,3.0,3.3,3.6,0.375,4.0,3.666666667,3.666666667,4.0,4.0,3.666666667,3.0,3.666666667,3.75,4.0,4.0,3.8,3.8,20,16,19,19,2,A,1,2,0,0,1,1,5,4,3,4,4,4,4,4,4,4,4,4,3,3,4,4,4,4,4,4,2,4,4,4,4,4,4,3,3,4,4,4,4,4,4,4,4,4,4,5,3,5,5,3,3,4,4,5,5,3,2,3,4,4,4,2,4,3,3,3,4,2,2,2,4.375,4,3.833333333,3.8,3.6,3.6,4.0,4.0,3.833333333,4.5,3.5,4.666666667,3.333333333,3.0,4,2,2,2,5.0,5.5,4.5,4.5,5.0,24,0,6.0,0,1,0,1,1,1,1,1,0,2,1,1,0,1,0,1,1,1,0,1,0,1,1,1,5,4,3,4,4,4,4,4,4,4,4,4,3,4,4,4,5,1,1,0,1,1,1,0,1,1,1,1,1,1,1,0,1,0,1,1,1,3,4,3,4,4,4,3,4,4,4,4,4,3,4,4,3,0.5,4.9,0.1,5.7855,-0.7855,5.827,6.805,5.238,5.331,5.725,0.7855,魏金霞,1.0,"人工智能在石油与天然气领域的应用
中心问题：围绕人工智能在石油与天然气的战场选择与控制方面的应用讨论
背景：石油与天然气领域中实地勘测选址的难度和效率受到人力效率的限制，对以往数据的使用没有达到充分水平，对现有数据的收集也面临低效问题。人工智能的介入能够将数据库中的数据整理分析，得出相对人工更快、较为多样的相对优选答案。人工智能的介入主要体现以下几个特点的应用：
1.思维链：人工智能的数据分析产出是一个输入输出的过程，不断优化思维链，可以在一个相对完善的基础上得到提升，更省力。
2.举一反三：人工智能数据库可以提供同类方案的解决方法做借鉴，以见面同类问题再发生时带来的重复性劳动。",,,,,,,,,
101010,3,1003,协调员,3.0,5.0,4.0,4.0,3.0,5.333333333333333,5.0,4.0,2.6666666666666665,3.0,3.0,6.0,5.666666667,5.333333333,5.0,3.987037037,3.922222222,3.533333333,3.2,5.2,4.2,4.5,0.25,4.2,5.0,4.666666667,4.2,5.0,4.666666667,4.0,4.666666667,3.75,3.4,4.0,4.2,3.6,17,16,21,18,3,A,1,2,0,0,1,1,7,4,4,3,4,4,4,2,4,4,4,4,4,5,4,4,5,5,4,4,4,4,4,5,4,4,4,4,4,5,4,5,5,5,5,4,4,4,5,4,4,4,4,4,4,4,4,4,2,4,3,3,2,2,4,3,4,3,4,4,4,3,3,4,5.125,4,3.5,4.0,4.2,4.0,4.8,4.4,4.5,4.0,4.0,3.333333333,3.666666667,2.5,4,3,3,4,6.0,4.5,5.0,5.0,6.0,22,0,8.0,0,1,1,1,1,1,1,1,0,2,1,1,1,2,1,1,1,1,1,2,1,2,1,1,8,5,5,4,5,5,5,4,4,4,4,5,2,3,3,3,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,1,1,8,5,5,4,5,5,5,4,4,4,4,5,2,3,3,3,0.863636364,5.3,1.7,6.1855,0.8145,6.827,5.805,5.738,5.831,6.725,0.8145,陈楚昕,1.0,"冷门绝学：文物修复
AI的帮助：
识别裂痕，AI提供修复指导（图片点对点，修复措施）
挑战：
1.需要更精密的修复手法，机械可能无法提供（解决方案：）
2.过于冷门，训练集不够，AI学习不足（解决方案：完整记录专家修复过程）
如何利用AI技术助力该学科的传承、创新与社会应用： 
1.记录技法，生成图片、视频用于教学。
2.自动生成修复方案，为专业人士提供修复方案参考，同时指导初学者学习
以上都有助于文物修复的传承与发展。",4.290223024928568e-10,0.2253521126760563,0.144927536231884,0.2253521126760563,0.016576513697435,0.4289360705147441,0.1445003896951675,0.4094488188976378,0.0205290169759179
101013,3,1003,启发员,4.0,7.0,4.0,4.0,4.0,4.666666666666667,3.333333333333333,4.333333333333333,4.0,4.0,3.333333333333333,5.0,5.0,5.0,5.0,4.0,4.0,4.0,4.0,4.8,4.4,4.7,0.125,4.0,4.0,4.0,3.8,4.0,3.666666667,4.0,4.0,4.0,3.6,4.0,4.0,4.0,18,16,20,20,3,A,1,1,0,0,1,1,8,8,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,4,4,4,3,3,3,3,3,4,4,2,3,3,4,3,3,3,4,3,3,4,3,8.0,8,4.0,4.0,4.0,4.0,4.0,4.0,3.833333333,3.5,3.5,3.0,3.333333333,3.25,3,3,4,3,5.5,4.5,5.0,4.5,5.5,21,0,8.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,8,4,4,3,4,4,4,4,4,4,4,3,2,4,3,3,9,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,8,4,4,4,4,4,4,4,4,4,4,4,2,4,4,4,0.772727273,5.0,3.0,5.8855,2.1145,6.327,5.805,5.738,5.331,6.225,2.1145,李运宏,2.0,"学科选择：文物修复
问题：请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
Ans:
其在人工智能时代可能焕发新生命力的可能原因在于数据库样例的丰富，即用AI技术将大多数的修复方案保存下来，为文物修复提供更多的参考依据和模板，在一个就是对于修复提供多种修复方式，使得人们能够了解到更好、更多的修复措施来实现文物更好的还原。
存在的挑战包括
需要更精密的修复手法，机械可能无法提供
过于冷门，训练集不够，ai学习不足（解决：完整记录专家修复过程）
最后一问：自动生成修复方案。比如：记录技法，生成图片、视频用于教学，提供事例，面向初学者帮助更大。",1.548032416972904e-05,0.0952380952380952,0.0327868852459016,0.0952380952380952,0.0363007192292034,0.278826897709944,0.1614250093698501,0.2895927601809955,0.0378177309361438
101021,3,1003,记录员,7.0,11.0,2.0,2.0,2.6666666666666665,2.0,4.0,2.333333333333333,3.6666666666666665,1.0,2.0,3.333333333,3.666666667,2.666666667,2.333333333,2.817592593,3.905555556,3.433333333,2.6,2.2,3.4,3.8,0.25,3.2,2.666666667,2.333333333,3.4,3.0,2.333333333,4.0,3.666666667,1.25,2.4,2.0,2.6,2.6,12,8,13,13,3,A,1,3,0,0,1,1,3,5,3,2,3,4,4,3,3,4,3,3,3,2,2,4,3,3,2,2,2,3,2,2,2,2,2,2,2,4,4,4,4,4,2,2,2,3,4,2,3,3,3,3,3,2,2,3,3,4,4,3,4,4,4,2,2,3,2,3,3,3,2,1,4.25,5,3.166666667,3.2,2.2,2.0,4.0,2.6,2.666666667,2.75,2.5,2.666666667,3.666666667,3.5,3,3,2,1,5.5,4.5,5.0,5.0,6.0,21,1,4.0,0,2,1,2,1,2,1,1,1,2,1,2,0,2,1,2,0,2,0,2,0,2,1,2,3,3,2,2,3,3,3,2,4,4,4,3,3,2,2,2,3,0,2,1,2,1,2,1,2,0,2,1,2,1,2,0,2,0,2,1,2,3,3,2,2,3,3,2,3,3,3,4,3,4,1,1,1,0.590909091,5.2,-2.2,6.0855,-3.0855,6.327,5.805,5.738,5.831,6.725,3.0855,颜道明,3.0,"学科：文物修复
ai的帮助：
拍下文物的裂痕，ai提供修复指导，利用图片点对点的方式，提供修复措施
挑战：
需要更精密的修复手法，机械可能无法提供
过于冷门，训练集不够，ai学习不足（解决方法：完整记录专家修复过程）
ai助力：自动生成修复方案。比如：记录技法，生成图片、视频用于教学，提供事例，面向初学者。",1.3344733570816925e-08,0.16,0.125,0.16,0.019348597226701,0.3744356633753483,0.1185203790664672,0.3695652173913043,0.0241228070175438
101000,4,1004,记录员,10.0,0.0,4.0,5.0,4.333333333333333,2.0,5.0,4.666666666666667,4.666666666666667,2.0,2.6666666666666665,4.0,4.666666667,3.0,4.333333333,3.462037037,3.772222222,3.633333333,3.8,4.3,3.5,4.6,0.375,3.8,4.333333333,4.333333333,4.0,3.666666667,4.0,3.5,3.0,3.75,3.8,4.25,3.4,3.6,19,17,17,18,4,A,1,3,0,0,1,1,8,7,4,4,4,4,4,4,3,3,4,3,4,3,4,4,4,4,4,4,4,4,4,4,3,3,3,4,3,4,4,4,4,4,3,3,3,3,3,4,4,4,3,2,2,4,2,3,2,3,2,4,3,2,4,2,2,2,4,4,4,1,2,3,7.375,7,4.0,3.4,4.0,3.2,4.0,3.0,3.833333333,3.75,2.5,2.333333333,3.666666667,2.25,4,1,2,3,3.5,3.5,5.0,6.0,5.5,21,0,8.0,1,1,1,1,1,1,1,2,1,2,1,1,1,1,0,1,1,1,0,2,0,1,1,1,6,4,5,3,4,3,4,4,4,4,4,4,3,2,2,4,8,0,1,1,1,0,2,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,4,5,4,4,4,5,4,4,4,4,3,2,2,2,2,0.681818182,4.7,3.3,5.5855,2.4145,4.327,4.805,5.738,6.831,6.225,2.4145,张菲煊,2.0,"冷门：古代传统（研究敦煌，埃及、甲骨文等研究古代文化的，极需传承的，国学范畴的学科），少数民族领域
选择的冷门学科：考古学（敦煌学）
发展挑战：
考古条件艰苦（环境条件艰苦）；
经验强相关性，较依赖过去经验，成功成本高；
需要传承，在人才培养方面遇到困难；
有很大的文本工作量，需要查阅的资料庞杂；
针对有无自成的知识体系有争议，研究成果在学术界交叉大
学科交叉复杂，涉及的学科多，研究方向多样，文物种类多
ai在其中助力的地方：
ai本身对环境要求低，基本只对网络和电源有要求
大模型可以解决学科交叉问题，可以更广泛的获取信息
能够快速阅读文本并从以前的资料中提取有用信息，有记忆效应
Ai面对的挑战
学科本身具有文学学科性质，ai可能难以解读其中的情感倾向
在环境艰苦的地方可能没有网络信号
学科本身对经验依赖强，Ai能够阅读的信息和资料可能比较少，且难以判断其真实性",9.900485278419935e-10,0.0531914893617021,0.043010752688172,0.0531914893617021,0.019657399606852,0.342313743562068,0.1787121593952179,0.3463203463203463,0.0198422793182396
101020,4,1004,协调员,7.0,10.0,4.0,5.0,5.0,5.0,5.333333333333333,3.6666666666666665,2.333333333333333,2.0,2.333333333333333,5.0,5.0,5.0,5.0,4.096296296,4.577777778,4.466666667,2.8,4.7,4.6,4.6,0.25,4.0,4.0,2.0,4.2,4.0,2.333333333,4.0,3.0,2.0,4.0,2.75,5.0,4.0,20,11,25,20,4,A,1,2,0,0,1,1,8,7,4,3,4,4,4,4,4,4,4,4,4,4,5,5,5,5,4,5,1,1,5,4,4,4,4,5,5,4,4,4,4,4,4,5,5,5,4,5,5,5,5,4,4,4,4,2,2,4,1,4,1,1,4,1,2,3,4,4,4,3,3,6,7.375,7,3.833333333,4.0,3.2,4.4,4.0,4.6,4.666666667,5.0,4.0,2.0,4.0,1.0,4,3,3,6,6.0,4.0,5.0,6.5,6.0,17,1,6.0,1,1,1,1,1,1,1,1,0,1,1,1,0,1,0,2,0,1,1,1,0,2,0,1,7,3,2,2,4,4,4,5,3,5,3,5,2,4,1,2,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,7,2,2,2,4,4,4,4,4,4,4,4,2,2,2,2,0.636363636,5.5,2.5,6.3855,1.6145,6.827,5.305,5.738,7.331,6.725,1.6145,王俊杰,3.0,"冷门：古代传统（研究敦煌，埃及、甲骨文等研究古代文化的，极需传承的，国学范畴的学科），少数民族领域
冷门学科：考古学（敦煌学）
发展挑战：
考古条件艰苦（环境条件艰苦）；
经验强相关性，较依赖过去经验，成功成本高；
需要传承，在人才培养方面遇到困难；
有很大的文本工作量，需要查阅的资料庞杂；
针对有无自成的知识体系有争议，研究成果在学术界交叉大
学科交叉复杂，涉及的学科多，研究方向多样，文物种类多
ai在其中助力的地方：
ai本身对环境要求低，基本只对网络和电源有要求
大模型可以解决复杂的学科交叉问题
ai处理长文本能力强于普通人，效率更高
ai自身的记忆能力能够帮助解决传承问题，有更好的记忆力
面对的挑战
ai在处理文本与画面内容的时候可能处理的没有人类精确，无法分析作品包含的情感
过于艰苦的环境可能无法使ai运行（比如断网断电，沙尘无法扫描）
ai可能的幻觉现象导致研究不准确",9.81245877316017e-13,0.0578512396694214,0.05,0.0578512396694214,0.0133839825220934,0.3066739587237571,0.0644779428839683,0.3474576271186441,0.0145933440113898
101023,4,1004,启发员,6.0,12.0,3.0,3.0,3.333333333333333,3.6666666666666665,3.6666666666666665,3.6666666666666665,2.6666666666666665,3.0,3.333333333333333,3.666666667,4.333333333,4.0,4.333333333,3.750925926,3.505555556,3.033333333,3.2,2.9,3.9,3.7,0.0,3.2,4.0,3.666666667,3.6,3.333333333,3.0,4.0,4.0,3.5,5.0,5.0,5.0,5.0,25,20,25,25,4,A,1,1,0,0,1,1,7,4,3,3,4,4,4,4,4,4,4,4,4,3,3,3,4,4,3,4,3,3,3,4,4,3,4,3,3,4,4,4,4,3,4,3,4,4,3,4,4,4,3,3,3,4,4,4,4,4,2,4,2,3,3,2,3,3,4,4,3,3,2,3,5.125,4,3.666666667,4.0,3.4,3.4,3.8,3.6,3.333333333,3.75,3.5,3.666666667,3.666666667,2.25,3,3,2,3,3.5,3.5,5.0,6.0,5.5,19,0,4.0,1,1,0,2,1,1,1,1,1,2,0,1,0,1,1,1,0,2,1,2,0,2,0,2,3,4,3,2,3,3,4,4,4,3,3,4,3,4,3,3,5,1,2,1,1,0,2,0,1,1,1,1,1,0,1,1,2,1,2,0,1,5,4,3,4,4,4,4,3,3,3,3,4,3,3,3,3,0.545454545,4.7,2.3,5.5855,1.4145,4.327,4.805,5.738,6.831,6.225,1.4145,李韦伊,1.0,"什么算冷门学科
冷门学科的发展挑战
为什么ai可以助力这个学科，人工智能在其中扮演什么样的角色
冷门：古代传统（研究敦煌，埃及、甲骨文等研究古代文化的，极需传承的，国学范畴的学科），少数民族领域
冷门学科：考古学（敦煌学）
发展挑战：
考古条件艰苦（环境条件艰苦）；
经验强相关性，较依赖过去经验，成功成本高；
需要传承，在人才培养方面遇到困难；
有很大的文本工作量，需要查阅的资料庞杂；
针对有无自成的知识体系有争议，研究成果在学术界交叉大
学科交叉复杂，涉及的学科多，研究方向多样，文物种类多
ai在其中助力的地方：
ai本身对环境要求低，基本只对网络和电源有要求
大模型可以解决学科交叉问题
能快速阅读文本并从以前的资料中提供有用信息
面对的挑战
ai难以解读学术研究其中的情感倾向
环境艰苦的地方可能存在网络的电源
AI能够阅读的信息以及前任资料比较少",5.926395462630067e-08,0.0847457627118644,0.0689655172413793,0.0847457627118644,0.0244262667575551,0.4051180601168425,0.1922199726104736,0.36,0.0275008594018563
102006,9,1009,启发员,4.0,991.0,2.0,2.0,1.6666666666666667,4.333333333333333,6.0,2.6666666666666665,2.333333333333333,4.0,3.6666666666666665,6.0,4.666666667,5.0,5.666666667,3.636111111,3.816666667,3.9,3.4,4.6,4.1,3.7,0.375,4.0,4.0,4.0,4.0,3.0,3.333333333,4.0,4.333333333,4.0,4.2,5.0,5.0,5.0,21,20,25,25,9,A,1,1,0,0,0,1,7,6,2,2,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,2,2,2,4,3,2,4,1,1,4,4,4,2,2,4,6,6.375,6,3.333333333,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,2.333333333,3.333333333,2.0,2,2,4,6,5.5,3.0,5.0,5.5,5.5,18,0,8.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,0,2,0,1,1,1,8,4,2,4,3,4,2,4,4,4,4,4,4,3,4,4,9,1,1,1,1,0,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,9,4,4,4,4,4,4,4,4,4,4,4,2,4,4,4,0.681818182,4.9,2.1,5.7855,1.2145,6.327,4.305,5.738,6.331,6.225,1.2145,朱耔潓,2.0,"讨论主题：请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。
冷门学科：
考古学
焕发生命力的原因：
可以增强判断的准确性，减少人工失误，AI和人工结合，可以极大的增强准确性；
AI面对一些肯定性的问题，可以严格根据法律，增强司法公正性；
AI可以处理分析大量的地形地貌，更模拟推断出该地形有没有遗迹，传统考古在探查时也在使用工具；
AI可以短时间内结合大量文献去分析制定该文物的挖掘方法。
可能的挑战：
AI缺乏创新力，无法像人类一样去提出有价值的创新点。
面对伦理问题，AI无法做到司法公正；
面对未知文物，AI无法做出准确判断，判断失误可能使得文物损坏。",3.0436298372215503e-10,0.1627906976744186,0.1428571428571428,0.1627906976744186,0.0156073345799033,0.3352710138751498,0.1294226348400116,0.3804347826086957,0.0205357142857143
102015,9,1009,记录员,5.0,996.0,3.0,3.0,6.0,3.6666666666666665,4.333333333333333,4.666666666666667,1.0,3.0,3.0,6.0,5.666666667,4.666666667,5.333333333,3.741666667,4.45,3.7,3.2,4.6,4.3,4.2,0.5,3.2,3.666666667,4.0,4.0,4.333333333,4.0,4.0,3.666666667,3.75,3.2,3.75,4.2,4.0,16,15,21,20,9,A,1,3,0,0,0,1,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,3,3,3,4,3,5,5,5,3,3,3,3,3,5,5,3,4,4,3,3,3,3,2,3,1,3,2,2,3,2,2,4,5,5,4,1,3,3,5.0,5,4.0,4.0,4.0,3.2,4.4,3.0,4.0,4.25,3.25,2.333333333,3.0,1.75,4,1,3,3,6.0,3.5,5.5,6.0,6.5,21,0,7.0,0,1,1,1,1,1,1,1,1,2,1,1,0,1,0,1,1,1,1,1,0,1,0,1,8,4,4,4,4,5,4,4,4,4,4,4,3,3,3,3,7,1,1,1,1,0,1,0,1,1,1,1,1,0,1,1,1,0,1,1,1,7,4,4,4,4,4,3,3,3,3,4,3,3,3,3,3,0.590909091,5.5,-0.5,6.3855,-1.3855,6.827,4.805,6.238,6.831,7.225,1.3855,蔡雅玲,3.0,"讨论主题：请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。
冷门学科：
考古学
焕发生命力的原因：
可以增强判断的准确性，减少人工失误，AI和人工结合，可以极大的增强准确性；
AI面对一些肯定性的问题，可以严格根据法律，增强司法公正性；
AI可以处理分析大量的地形地貌，更模拟推断出该地形有没有遗迹，传统考古在探查时也在使用工具；
AI可以短时间内结合大量文献去分析制定该文物的挖掘方法。
可能的挑战：
AI缺乏创新力，无法像人类一样去提出有价值的创新点。
面对伦理问题，AI无法做到司法公正；
面对未知文物，AI无法做出准确判断，判断失误可能使得文物损坏。
个人看法：我觉得在考古学方面，AI可以起一个辅助的作用，分析大量文献，可以极大的增加考古的效率；但是在面对一些未知文物时，还是需要专业的人士去做出判断，如果AI根据他所有的知识库去模糊匹配一些文物，可能会对文物造成不可估量的损失，未知文物所需的新方法、新手段，AI也无法提供。",6.196799348428193e-10,0.0704225352112676,0.0638297872340425,0.0704225352112676,0.0169142524558468,0.4059200897408767,0.2111499309539795,0.4291044776119403,0.0222612770943174
102017,9,1009,协调员,5.0,997.0,4.0,5.0,2.0,2.333333333333333,2.333333333333333,4.666666666666667,2.333333333333333,2.0,2.333333333333333,3.333333333,3.333333333,3.666666667,2.666666667,4.025,4.15,3.9,4.4,3.4,4.6,4.4,0.0,3.8,4.0,2.666666667,3.8,4.0,2.666666667,4.0,4.0,4.25,3.8,4.25,4.2,4.2,19,17,21,21,9,A,1,2,0,0,0,1,6,6,2,3,3,3,3,4,4,4,4,4,4,4,4,5,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,5,3,4,4,4,4,3,3,4,4,4,2,3,4,3,3,3,6.0,6,3.0,4.0,4.0,4.0,4.0,4.0,4.166666667,3.75,4.0,4.0,3.666666667,3.75,4,3,3,3,5.0,3.0,6.0,3.5,6.0,21,0,10.0,0,1,0,2,0,2,1,2,1,2,1,1,0,1,0,1,0,2,0,2,1,2,0,2,3,3,2,3,4,4,4,4,3,4,4,4,4,2,3,2,3,1,2,1,2,0,2,1,2,1,2,1,2,0,2,1,2,1,2,1,2,2,3,2,3,4,4,4,4,4,4,3,4,4,2,2,2,0.545454545,4.7,1.3,5.5855,0.4145,5.827,4.305,6.738,4.331,6.725,0.4145,卢迅,1.0,"讨论主题：请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。
冷门学科：
考古学
焕发生命力的原因：
可以增强判断的准确性，减少人工失误，AI和人工结合，可以极大的增强准确性；
AI面对一些肯定性的问题，可以严格根据法律，增强司法公正性；
AI可以处理分析大量的地形地貌，更模拟推断出该地形有没有遗迹，传统考古在探查时也在使用工具；
AI可以短时间内结合大量文献去分析制定该文物的挖掘方法。
可能的挑战：
AI缺乏创新力，无法像人类一样去提出有价值的创新点。
面对伦理问题，AI无法做到司法公正；
面对未知文物，AI无法做出准确判断，判断失误可能使得文物损坏。",8.258484755193565e-10,0.1076923076923076,0.09375,0.1076923076923076,0.0161633943584529,0.4463227061386312,0.1838699132204055,0.4076086956521739,0.0217391304347825
102002,10,1010,启发员,8.0,989.0,0.0,0.0,4.0,4.333333333333333,4.0,3.0,4.333333333333333,2.6666666666666665,2.333333333333333,4.666666667,3.666666667,3.333333333,5.666666667,3.965740741,3.794444444,3.766666667,3.6,4.6,2.8,3.6,0.375,3.6,4.0,2.666666667,3.0,3.0,2.0,5.0,4.0,3.75,3.6,2.25,3.6,3.2,18,9,18,16,10,A,1,1,0,0,0,1,8,6,3,4,4,4,4,3,3,3,3,3,4,3,3,3,5,4,5,4,3,2,3,2,2,2,2,3,3,4,4,4,4,4,3,3,3,3,4,3,3,3,4,4,3,4,3,3,3,4,3,4,3,3,4,2,3,4,5,5,4,3,4,4,6.75,6,3.666666667,3.2,2.8,2.4,4.0,3.2,3.833333333,3.25,3.5,3.0,4.0,2.75,4,3,4,4,7.0,6.5,6.5,7.0,7.5,20,1,5.0,0,1,1,2,1,1,1,1,0,2,1,1,0,1,1,1,1,1,1,1,1,1,1,2,5,2,2,2,3,3,3,4,3,2,2,4,3,3,2,2,6,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,6,4,2,2,4,4,4,4,4,3,4,3,2,3,3,2,0.772727273,6.9,1.1,7.7855,0.2145,7.827,7.805,7.238,7.831,8.225,0.2145,权煜然,2.0,"冷门学科：
古文字学
可能原因：
语言的特征可以运用人工智能挖掘，在结构上发现其独特性；
运用一些相关的算法进行语义预测，磅数现代人理解失传的语言
人工智能有实现对语音语调的识别与生成
可能挑战：
现有的知识储备有限，无法提供大量数据
人类自身对于学科理解可能存在偏差
如何利用AI技术助力该学科的传承、创新与社会应用：
运用神经网络识别古文字的含义；
运用人工智能处理数据集，在其基础上进行扩充；
应用方面：可以对考古进行补充，厘清历史；推进语言学的研究；挖掘语言的文化；还可以还原古代语言的发音，创设某些文学作品的历史语境；通过人工智能技术识别文字手稿，构建数据库；利用AI将不同的语言进行对比研究，推进语言学的发展；构建古文字学智能体，辅助古文字学相关研究，如帮助整理资料；利用AI构建该学科与大众的桥梁，推动冷门绝学深入大众生活；",8.597149159435204e-22,0.0191693290734824,0.0128617363344051,0.0191693290734824,0.0072636955808613,0.1863324581541717,0.1447916626930236,0.3961352657004831,0.0085614628474791
102009,10,1010,协调员,7.0,993.0,3.0,3.0,2.333333333333333,3.6666666666666665,4.333333333333333,3.6666666666666665,3.0,3.333333333333333,3.6666666666666665,4.333333333,4.666666667,4.0,5.0,3.662962963,3.977777778,3.866666667,3.2,4.0,3.5,4.3,0.375,4.8,5.0,5.0,3.8,4.0,4.0,4.0,5.0,4.0,3.6,4.0,4.4,4.0,18,16,22,20,10,A,1,2,0,0,0,1,7,7,4,4,4,4,4,4,4,3,4,3,4,4,4,4,4,4,3,4,4,3,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,4,4,4,2,3,3,3,3,4,2,2,3,7.0,7,4.0,3.6,3.8,4.0,3.8,4.0,3.833333333,4.0,3.75,3.666666667,4.0,3.5,4,2,2,3,6.5,5.0,5.0,5.0,6.0,19,0,6.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0,2,1,2,1,2,1,1,6,4,4,4,4,4,4,4,4,4,3,4,4,4,3,4,7,1,1,1,1,1,1,0,1,1,1,1,1,1,1,0,2,0,1,1,1,8,5,5,5,5,5,5,5,5,5,4,5,3,4,3,3,0.727272727,5.5,1.5,6.3855,0.6145,7.327,6.305,5.738,5.831,6.725,0.6145,王鑫,1.0,"古语言学的延续发展
机遇：ai的强大算力可以在现有资料中找到规律，破解文字背后的意义。还原古语言的语法体系。Ai生成的模型可以开放使用，降低公众对本学科的认知门槛，吸引更多人参与到古语言的学科研究中。
挑战：语料库内容少，现有研究资料不足，对本学科研究成果不够成熟，无法直接供ai使用。
方法：算法分析古语言句法，图片识别相似字体，破解文字本义。使用大模型联系语义，语音，字形，还原文字。",7.15845573512273e-08,0.0857142857142857,0.0588235294117647,0.0857142857142857,0.0181417188389299,0.3378370689744622,0.1300026923418045,0.3391304347826087,0.025177533892834
102014,10,1010,记录员,5.0,996.0,3.0,4.0,4.0,3.333333333333333,5.0,3.333333333333333,3.0,3.333333333333333,2.6666666666666665,4.333333333,4.0,4.0,4.0,4.137962963,3.827777778,3.966666667,3.8,4.0,4.3,4.4,0.875,4.0,4.333333333,2.666666667,4.2,4.0,2.333333333,4.0,4.0,3.5,3.8,3.25,4.2,4.2,19,13,21,21,10,A,1,3,0,0,0,1,7,7,4,4,4,5,5,5,4,4,4,3,4,4,4,5,5,5,5,4,4,3,5,5,4,4,4,4,4,4,4,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,1,4,2,1,5,1,4,4,4,4,5,1,1,4,7.0,7,4.5,3.8,4.2,4.0,4.6,4.0,4.666666667,4.0,4.0,4.0,4.666666667,1.25,5,1,1,4,7.0,5.5,6.5,6.5,7.0,23,1,6.0,1,1,1,1,1,1,1,1,0,2,1,1,0,2,0,2,0,2,1,2,0,2,0,2,5,3,2,2,4,4,4,4,4,4,4,5,2,4,2,2,8,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,2,0,2,1,1,8,4,2,2,4,5,4,4,4,4,4,4,2,4,4,2,0.681818182,6.5,0.5,7.3855,-0.3855,7.827,6.805,7.238,7.331,7.725,0.3855,刘儒骁,3.0,"冷门学科：古文字学
可能原因：
语言的特征可以运用人工智能挖掘，在结构上发现其独特性；
人工智能有实现对语音语调的识别与生成
可能挑战：
现有的知识储备有限，无法提供大量数据；
目前该学科对语言深层次的内容理解可能有问题，给予AI的信息不一定准确
如何利用AI技术助力该学科的传承、创新与社会应用：
运用神经网络识别古文字的含义；
运用人工智能处理数据集，在其基础上进行扩充；
应用方面：可以对考古进行补充，厘清历史；推进语言学的研究，帮助整合相关资料；还可以还原古代语言的发音，创设某些文学作品的历史语境；通过人工智能技术识别文字手稿，构建数据库；可以运用算法进行语义预测
还可以利用创新技术生成新的文本、音频等，推广古文字学的研究，让大众有机会认识到该学科",1.8001081705569115e-05,0.0666666666666666,0.0344827586206896,0.0666666666666666,0.036370903277378,0.3723260139401489,0.166106641292572,0.291005291005291,0.0387143900657414
102016,11,1011,记录员,9.0,997.0,2.0,2.0,2.6666666666666665,1.3333333333333333,3.333333333333333,3.0,5.0,2.333333333333333,2.0,4.666666667,4.333333333,2.666666667,4.333333333,2.660185185,2.961111111,1.766666667,2.6,2.9,3.7,4.6,0.125,2.4,3.333333333,1.666666667,2.4,3.0,1.333333333,4.0,4.333333333,4.25,3.6,2.5,4.0,4.2,18,10,20,21,11,A,1,3,0,0,0,1,3,5,2,2,4,4,4,4,4,4,4,4,4,3,2,2,2,4,4,2,2,2,2,2,2,2,2,3,2,2,2,2,2,2,2,2,2,2,2,3,2,3,3,3,3,3,3,4,3,4,5,4,5,4,4,5,4,3,3,3,3,1,2,1,4.25,5,3.333333333,4.0,2.0,2.2,2.0,2.0,2.833333333,2.75,3.0,3.666666667,4.0,4.75,3,1,2,1,7.5,6.5,6.5,6.5,7.5,22,0,3.0,0,2,1,1,1,2,1,2,0,2,1,1,0,1,1,1,0,2,0,2,1,1,1,2,3,2,1,1,3,3,3,2,2,2,4,2,2,2,2,2,3,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,0,2,1,1,6,2,1,2,3,3,4,3,3,2,2,2,2,2,2,3,0.636363636,6.9,-3.9,7.7855,-4.7855,8.327,7.805,7.238,7.331,8.225,4.7855,张滢,3.0,"1.具有发展潜力的冷门学科：古生物学
2.焕发新生的原因：
（1）AI可以通过图像识别和机器学习技术，帮助科学家更快速地识别和分类化石，分析其中蕴含的符号内涵并进行分析、联系。
（2）利用AI模拟古环境和生物演化过程，帮助我们预测新的化石或者新的物种可能出现的位置，帮助我们发掘或者。
（3）AI可以整合其它学科的相关数据，比如地质学、气候学、生态学等多个学科的数据，为古生物学提供更全面的分析框架。
3.在未来发展中面临的可能挑战：
（1）古生物学已有的研究比较少，可供AI分析的 数据和资料有限。
（2）挤压相关研究人员的研究空间或者增加大家的发掘压力。如果AI不断提供新的可研究地点或者化石，需要合理分配人力进行研究；而且会导致相关研究在一定时间内停滞不前。
（3）需要古生物-人工智能的跨学科人才，不同领域的专家在相关领域具有权威性，但其它领域的专家可能理解不了。",4.602886842571637e-06,0.208955223880597,0.1515151515151515,0.208955223880597,0.0319789315274642,0.4330382372884942,0.1725263148546219,0.3593073593073593,0.0355156183140779
102022,11,1011,协调员,7.0,1000.0,3.0,3.0,4.333333333333333,4.0,5.333333333333333,4.666666666666667,3.333333333333333,2.6666666666666665,3.6666666666666665,4.666666667,4.666666667,5.0,5.0,4.116666667,4.7,4.2,4.2,4.3,4.2,4.2,0.25,3.4,3.666666667,3.666666667,4.0,4.333333333,3.666666667,5.0,4.333333333,2.25,3.2,3.5,3.4,4.0,16,14,17,20,11,A,1,2,0,0,1,1,5,8,3,4,4,4,4,4,4,3,3,3,4,3,1,3,3,3,3,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,1,1,1,1,4,3,3,3,2,4,4,3,4,3,2,4,3,2,4,4,2,3,1,3,1,6.875,8,3.833333333,3.4,1.0,1.0,1.2,2.0,2.666666667,1.0,3.25,2.666666667,4.0,2.75,3,1,3,1,5.0,3.0,6.0,5.0,5.0,18,0,8.0,0,2,1,1,1,2,1,1,1,1,1,1,0,1,0,1,1,2,1,2,0,1,1,1,7,4,4,3,5,5,3,4,4,3,4,5,3,4,3,4,8,1,1,1,1,0,2,1,1,1,1,1,1,1,1,1,2,0,2,1,1,8,4,4,3,4,4,3,3,3,3,3,5,3,4,2,2,0.727272727,4.8,0.2,5.6855,-0.6855,5.827,4.305,6.738,5.831,5.725,0.6855,孙恒米南,2.0,"人工智能时代的地质勘探
当今时代的地质勘探需要人类去到现场进行样本采集，再带回进行分析。但人工智能完全可以自主进行样本采集，并可以比人类进行更加精确的位置测算，平均采集，并可以预测样本的品质，直接开始更加精确的鉴定与检测。效率会大大提升。地质勘探过程中人为因素影响可能巨大，机器往往比人类具有更加理性精确的判断与行动。
但也会面临数据不能活用，或人类的灵感被遏制的难题。
ai应该辅助人类完成任务，并由人类时时监控操作。人也应更多参与，合理利用ai得到基础数据，但高端处理还是要自己动手。",8.563264033992565e-05,0.1379310344827586,0.074074074074074,0.1379310344827586,0.0410277105192359,0.2985055259232985,0.1196484640240669,0.25,0.0432098765432098
102012,12,1012,记录员,2.0,995.0,3.0,3.0,2.6666666666666665,6.0,6.0,6.0,2.0,3.6666666666666665,3.333333333333333,6.0,6.0,6.0,6.0,5.0,5.0,5.0,5.0,5.7,5.3,5.3,0.625,4.0,4.0,3.333333333,3.4,3.666666667,3.333333333,4.5,5.0,5.0,5.0,5.0,5.0,5.0,25,20,25,25,12,A,1,3,0,0,0,1,9,9,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,2,5,1,3,4,4,4,5,5,3,6,9.0,9,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,3.666666667,4.333333333,2.75,5,5,3,6,6.0,3.5,5.5,5.5,6.0,19,1,8.0,0,1,1,1,0,1,1,1,1,1,0,1,0,1,0,1,1,1,1,1,1,2,1,1,8,4,2,4,3,4,4,4,4,3,3,3,3,3,3,4,8,1,2,1,1,0,1,1,1,1,1,1,1,0,1,0,1,0,1,0,1,8,4,2,4,4,4,4,4,4,4,4,4,4,4,4,3,0.545454545,5.3,3.7,6.1855,2.8145,6.827,4.805,6.238,6.331,6.725,2.8145,李承潞,2.0,"考古
人工智能时代可能焕发新生命力的可能原因
1.降低人工成本
2.提升识别效率
3.扩展考古面
在未来发展中面临的可能挑战
1.数据库录入不易
2.AI难以理解文物背后的内涵
3.AI技术可能提高技术门槛，对考古工作者提出更大挑战
如何利用AI技术助力该学科的传承、创新与社会应用
传承：利用AI学习考古专业知识
创新：文物数据计入AI数据库，形成中华考古体系
社会应用：AI讲解员，在博物馆讲解考古文物背后的故事",1.2188194658890042e-09,0.3076923076923077,0.2368421052631579,0.3076923076923077,0.0218316148830942,0.2667364265909303,0.1616775542497635,0.4285714285714285,0.023308957952468
102013,12,1012,协调员,9.0,995.0,6.0,7.0,6.0,4.666666666666667,6.0,5.666666666666667,1.0,3.6666666666666665,3.6666666666666665,6.0,5.0,6.0,6.0,4.402777778,4.416666667,3.5,5.0,5.5,5.2,5.9,0.625,4.8,5.0,3.0,4.8,4.0,3.333333333,5.0,4.0,4.25,3.0,3.5,3.6,5.0,15,14,18,25,12,A,1,2,0,0,0,1,9,9,5,4,5,5,5,5,5,5,5,5,5,3,5,5,5,2,4,5,5,3,4,2,4,5,5,5,4,5,5,4,5,5,4,4,4,5,5,5,4,4,5,5,5,4,4,3,2,4,1,4,2,1,4,1,3,5,5,5,5,4,4,6,9.0,9,4.833333333,5.0,3.8,4.6,4.8,4.4,4.0,4.5,4.5,2.666666667,4.0,1.25,5,4,4,6,6.5,3.5,5.5,6.0,6.5,20,1,7.0,0,1,1,1,1,1,1,1,0,2,0,2,0,1,0,1,0,1,1,2,0,1,0,1,5,4,3,3,4,5,3,5,4,5,5,5,3,2,5,4,7,1,1,1,1,0,1,1,2,0,1,1,1,0,1,0,1,1,1,1,1,6,3,2,4,5,5,5,5,5,5,5,4,3,2,5,4,0.454545455,5.6,3.4,6.4855,2.5145,7.327,4.805,6.238,6.831,7.225,2.5145,刘恩启,1.0,"考古学就非常冷门
工作量大减，通过数据库让AI进行大量的工作，比如
利用已有的进行预处理刚出土文物，识别一些基础信息
替代人类完成恶劣条件下的工作
AI在知识上可以协助人类，但是当需要人去完成的事情的时候，原来的挑战依旧存在
比如文物保存方面，AI明确环境但还是需要人去完成，原来保存环境差现在仍然差
AI无法了解文物的深层内涵及其历史意义，AI只能识别出这是一副字画，也可以知道里面是什么内容，但是他无法明白这可能是古代历史的一个重大事件记录，或者其他的意义
AI变相提高的加入行业的门槛，以前可能只挖土，现在既要挖土也要用AI，导致原有的人可能跟不上，新人所需要的教育水平提高
利用AI宣传此学科，宣传量大，受众群体才会更多，才有更多人加入传承
降低专业知识门槛，以前需要了解全部知识，利用AI则可以随时询问调取庞大的数据库，辅助研究",1.6780245155989218e-08,0.1636363636363636,0.1481481481481481,0.1636363636363636,0.0174914241075977,0.2584434919104283,0.1116837412118911,0.3318181818181818,0.0224822913458576
132006,5,1305,协调员,2.0,29991.0,2.0,3.0,3.0,3.0,4.666666666666667,5.666666666666667,6.0,4.666666666666667,3.333333333333333,5.333333333,4.0,3.0,5.0,4.302777778,4.816666667,3.9,3.4,2.9,2.9,5.4,0.375,3.8,5.0,4.666666667,2.8,2.666666667,3.0,5.0,4.0,3.5,4.4,4.25,3.8,5.0,22,17,19,25,5,B,1,2,0,0,1,1,8,5,5,5,5,4,4,4,2,3,3,3,3,3,2,4,3,2,4,4,4,4,3,3,3,4,4,4,3,5,5,3,5,5,4,4,4,4,4,4,4,5,5,3,3,4,3,3,3,3,1,3,2,5,3,1,1,3,3,3,5,3,1,2,6.125,5,4.5,2.8,3.6,3.6,4.6,4.0,3.0,4.5,3.25,2.333333333,3.0,2.25,5,3,1,2,6.0,4.5,4.0,4.5,5.0,18,0,4.0,1,1,1,1,1,2,1,1,1,1,1,1,0,1,0,1,1,1,0,1,0,2,0,2,3,3,2,4,3,2,3,4,2,4,2,2,5,4,3,3,9,0,1,1,1,0,1,0,2,1,1,1,1,1,1,0,2,0,2,1,2,7,5,4,5,5,5,5,5,5,5,1,3,3,5,5,4,0.545454545,4.8,3.2,5.6855,2.3145,6.827,5.805,4.738,5.331,5.725,2.3145,诸葛晓雪,1.0,"学科：考古学
换发生命力：通过AI识别系统，以及AI强大的段落理解能力，通过录入数据增强对考古文物的识别已经考古史料的理解能力，助力史料的开发与历史的研究；
通过人工智能的活化赋予文物新的生命力，增强传播能力，发挥其历史价值
挑战：基础数据库稀缺，大部分靠人工自行辨别形成，技术不成熟。
如何助力：增加数据库模型，增强文物开采和识别能力。",6.3573419460595104e-06,0.125,0.0666666666666666,0.125,0.0278318953520734,0.4974229717407937,0.0954835638403892,0.3404255319148936,0.0393401015228426
132014,5,1305,记录员,5.0,29994.0,5.0,5.0,2.6666666666666665,2.6666666666666665,4.333333333333333,3.6666666666666665,2.333333333333333,4.333333333333333,3.6666666666666665,4.666666667,4.333333333,4.666666667,4.666666667,4.15462963,3.927777778,3.566666667,3.4,4.2,3.7,4.4,0.375,4.6,4.0,3.0,4.4,3.666666667,3.333333333,3.5,3.666666667,3.75,3.2,4.25,3.8,4.0,16,17,19,20,5,B,1,3,0,0,1,1,6,6,3,3,4,4,2,4,4,4,4,3,4,5,5,3,4,4,5,3,4,2,4,2,4,2,3,3,3,4,4,4,3,4,3,4,3,4,5,4,4,4,5,4,4,5,5,2,4,4,2,4,4,3,4,2,3,3,3,4,4,3,4,4,6.0,6,3.333333333,3.8,3.0,3.0,3.8,3.8,4.333333333,4.25,4.5,3.0,4.0,2.75,4,3,4,4,7.0,5.5,5.5,5.0,6.0,23,1,8.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,7,4,2,4,4,4,3,4,4,4,5,5,2,4,3,4,7,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,7,4,2,3,4,4,4,5,5,4,5,4,3,5,4,4,0.818181818,5.8,0.2,6.6855,-0.6855,7.827,6.805,6.238,5.831,6.725,0.6855,王涵东,3.0,"我们小组选择的冷门学科是考古学中的古文字识别与分析.在当今时代,ai工具已经很有效果的利用到了各行各业,对于古文字这种比较小众且冷门的方向,对于ai的结合还不是很充分,所以结合恰当的人工智能学科知识,助力古文字在当今时代焕发新生机,对古老的知识进行整合与系统化整理.
对于这种古文字不像如今汉语有系统化的编码方式,如拼音,五笔等方式,所以更多的以图像的这一模态进行输入到神经网络中,可以先构建一个有效的编码规则,使得其输入到神经网络的信息更易于学习.通过已知古文字含义的文字当做已知语料库进行训练去预测未知古文字.
面临的挑战就是:当前古文字如甲骨文的数据集很小,神经网络很可能无法根据如此之少的数据进行有效的训练和推理.
对于其创新,可以利用其自然语言的特性,去创造一些符合古代模式的文学创作.更好的宣传古文字.",5.779245137306522e-07,0.0634920634920634,0.0327868852459016,0.0634920634920634,0.0221238938053097,0.1257284280606568,0.1061882153153419,0.2266009852216748,0.0230245944531658
132017,5,1305,启发员,4.0,29995.0,3.0,3.0,1.0,6.0,1.3333333333333333,6.0,6.0,1.6666666666666667,1.6666666666666667,5.333333333,6.0,4.333333333,6.0,4.760185185,3.561111111,4.366666667,4.2,5.1,5.3,4.6,0.0,3.6,3.333333333,2.333333333,1.6,2.0,1.0,2.0,2.0,4.0,2.0,2.5,1.6,2.8,10,10,8,14,5,B,1,1,0,0,1,1,6,6,2,3,2,4,4,2,2,1,1,3,3,3,2,2,2,4,3,3,3,2,2,2,2,2,2,2,2,3,2,4,4,4,3,3,3,3,2,2,2,2,2,2,2,2,2,3,2,2,3,3,2,2,3,3,2,2,3,4,3,2,3,3,6.0,6,2.833333333,2.0,2.4,2.0,3.4,2.8,2.666666667,2.0,2.0,2.333333333,2.666666667,2.5,3,2,3,3,5.5,4.0,4.0,5.0,5.0,20,1,9.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,9,1,1,1,3,1,2,1,1,4,1,1,1,3,1,1,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,7,3,1,3,4,4,2,5,1,4,4,4,2,1,1,3,0.818181818,4.7,1.3,5.5855,0.4145,6.327,5.305,4.738,5.831,5.725,0.4145,郭宇翔,2.0,"冷门学科：考古学
可能原因：由人无法识别的文字可由人工智能根据上下文推测出来，许多工作较为枯燥乏味可交由人工智能完成
挑战：数据库少，初期的学习训练模型的过程较为困难，超级对齐，许多甲骨文专业人十也无法判断究竟是何含义，无法对AI的预测做出合适的判断。
可利用AI学习该学科的基础知识从而使人数较少的学科得以传承，利用AI可以创新该学科的研究方法，促进该学科融入社会中，如用AI设计相关文创产品。",3.4195445625307505e-19,0.056338028169014,0.0428571428571428,0.056338028169014,0.006960845245494,0.2759179510007963,0.1262224167585373,0.3928571428571428,0.0098721112856181
132012,6,1306,记录员,4.0,29994.0,5.0,6.0,3.6666666666666665,4.0,4.0,5.0,3.0,4.0,2.0,5.0,5.333333333,4.333333333,5.666666667,3.948148148,4.688888889,4.133333333,3.8,5.1,4.2,5.1,0.625,4.2,4.666666667,4.333333333,4.4,4.333333333,4.0,4.5,4.333333333,4.25,3.4,3.25,4.2,4.6,17,13,21,23,6,B,1,3,0,0,1,1,7,7,4,3,4,4,4,4,3,4,4,4,4,5,5,5,4,3,4,5,5,4,4,4,3,3,3,3,3,5,5,4,5,5,4,4,4,4,4,4,5,5,4,5,4,4,4,3,3,4,2,4,2,2,4,2,3,4,4,4,4,3,3,5,7.0,7,3.833333333,3.8,4.4,3.0,4.8,4.0,4.333333333,4.5,4.25,3.0,4.0,2.0,4,3,3,5,5.0,3.5,3.5,4.0,4.5,18,1,9.0,0,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,8,4,3,5,5,4,4,5,5,5,2,5,2,2,2,2,10,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,1,1,7,5,3,5,5,4,5,5,5,4,2,5,2,4,4,4,0.727272727,4.1,2.9,4.9855,2.0145,5.827,4.805,4.238,4.831,5.225,2.0145,房金衡,2.0,"有关使用AI技术助力敦煌学的应用讨论
13012 房金衡
敦煌学，是研究、发掘、整理和保护中国敦煌文物、文献的综合性学科。以敦煌遗书、敦煌石窟艺术、敦煌学理论为主，兼及敦煌史地为研究对象。涉及敦煌学理论、敦煌学史、敦煌史事、敦煌语言文字、敦煌俗文学、敦煌蒙书、敦煌石窟艺术、敦煌与中西交通、敦煌壁画与乐舞、敦煌天文历法等诸多方面。
敦煌学原本主要是研究藏经洞出土的写本文献，以后逐渐扩大到石窟、壁画、汉简乃至周边地域出土的古代文献和遗存的古代文物。敦煌文化是在中原传统文化主导下的多元开放文化，敦煌文化中融入了不少来自中亚、西亚和中国西域、青藏、蒙古等地的民族文化成分，呈现出开放性、多元性、包容性。
但是敦煌学作为较冷门的科目，由于狭窄的就业前景，较少的社会关注度，以及大多数人未来的就业选择，敦煌学面临着后继无人的局面。我们经过思考和讨论，寻找一些可行方案，使用AI技术赋能敦煌学研究，助力敦煌文化的传承保护。
敦煌学作为一门人文历史性质的学科，有着深厚的历史底蕴和文化留存，所以我们寻找到的切入点，主要包括文物保护，文化传承两方面。
文物保护方面，敦煌文化遗址是我国的宝贵遗产，为了更好地保护现有的文物，我们可以使用AI技术助力文物修复，文物保存。
文化传承方面，使用AI数字化技术，我们一更好的传播形式展示敦煌文化，让敦煌文化被更多人了解，传承。",0.0001861609664051,0.123076923076923,0.0634920634920634,0.123076923076923,0.0383939610854428,0.3549408566796577,0.0860430896282196,0.2530487804878049,0.0466786355475763
132013,6,1306,协调员,6.0,29994.0,5.0,6.0,4.0,4.666666666666667,5.333333333333333,5.333333333333333,5.333333333333333,3.0,3.0,3.666666667,5.0,6.0,5.333333333,4.607407407,4.644444444,3.866666667,3.2,3.8,3.8,4.3,0.375,4.0,4.0,3.0,4.0,4.0,3.0,4.0,4.0,4.25,4.0,4.25,4.2,4.6,20,17,21,23,6,B,1,2,0,0,1,1,7,6,4,3,4,4,4,4,5,4,5,4,5,3,4,5,5,3,4,4,4,4,3,4,3,4,3,4,4,4,4,5,5,5,5,4,4,5,4,4,4,4,4,3,3,4,4,4,3,3,3,4,2,2,4,3,4,4,3,4,3,1,3,3,6.375,6,3.833333333,4.6,3.8,3.6,4.6,4.4,4.0,4.0,3.5,3.666666667,3.666666667,2.5,3,1,3,3,6.5,4.5,6.0,5.5,6.0,21,0,9.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,2,8,4,2,3,4,4,4,4,4,4,4,4,3,3,3,3,10,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,9,4,2,3,4,4,4,4,4,4,4,4,3,3,3,3,0.818181818,5.7,1.3,6.5855,0.4145,7.327,5.805,6.738,6.331,6.725,0.4145,杨诗佳,1.0,"敦煌学，敦煌学包括了敦煌石窟考古、敦煌艺术、敦煌遗书、敦煌石窟文物保护、敦煌学理论。在人工智能时代，可能焕发新生命力。ai在文物记录保存方面有得天独厚的优势，既可以形成知识图谱，又能够以数字生命等方式使各种文物以活灵活现的形式保存下来。Ai在环境检测、提供保护策略的方面也有优势。在文物成分分析，分类处理等领域，人工智能也能代替很多人工。在该学科的宣传方面，ai也可以使文化传播更方便便捷，使人容易接受。
	可能面临的挑战即是对已有的资料研究会有尽头，未来的发展趋势即使如何更好的保存和宣传。",,,,,,,,,
132018,6,1306,启发员,3.0,29995.0,3.0,3.0,5.0,4.666666666666667,5.0,2.333333333333333,3.0,4.0,3.333333333333333,5.0,4.666666667,3.666666667,4.0,3.996296296,3.977777778,3.866666667,3.2,3.8,3.8,4.0,0.25,4.0,3.666666667,3.0,4.0,5.0,2.666666667,4.0,4.0,4.0,4.0,3.75,4.4,5.0,20,15,22,25,6,B,1,1,0,0,1,1,6,6,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,2,2,2,2,6.0,6,3.0,3.0,3.0,3.0,3.0,3.0,3.0,3.0,3.0,3.0,3.0,3.0,2,2,2,2,6.5,8.5,7.0,7.0,8.0,19,0,7.0,0,1,1,1,1,1,1,1,0,2,1,1,1,2,1,2,1,1,1,1,1,1,1,2,7,4,2,2,5,5,5,4,4,4,4,4,3,4,3,3,7,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,1,0,2,0,2,8,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,0.727272727,7.4,-1.4,8.2855,-2.2855,7.327,9.805,7.738,7.831,8.725,2.2855,娜迪拉·多斯江,3.0,"用AI技术助力敦煌学的传承、创新与社会应用
用AI技术增加对于敦煌学的解读形式
敦煌学涉及大量的文献、壁画、雕塑等多模态数据。人工智能的自然语言处理技术可以对这些数据进行高效挖掘和分析，揭示其中的人物关系、时间线、地理信息等深层次内容，构建详实的历史模型。通过多模态学习，AI可以同时处理文本、图像、音频等多种数据类型，更全面地理解和解读敦煌文化。
用AI技术增加对于敦煌学修复形式
敦煌藏经洞出土的大量写本因历史原因被撕裂为多件，流散到世界各地。传统的人工缀合方法效率低下且难度较大。浙江大学的张涌泉教授和吴飞教授团队利用数据驱动和知识引导的新一代人工智能方法，构建了敦煌残卷缀合自动缀合算法
三．用AI技术增加对于敦煌学的表现形式
结合AI与 VR/AR技术，可以创建生动逼真的敦煌历史文化虚拟场景，让公众通过3D重建壁画或互动应用，直观感受和理解敦煌艺术的魅力及其背后的故事。这种沉浸式体验可以吸引更多人关注敦煌学，提升其社会影响力。",,,,,,,,,
132003,7,1307,记录员,7.0,29990.0,3.0,3.0,2.333333333333333,4.0,4.0,4.666666666666667,4.666666666666667,3.333333333333333,3.0,4.0,4.666666667,4.0,4.0,4.02962963,4.177777778,4.066666667,4.4,4.5,4.5,4.5,0.625,4.0,4.0,4.0,4.0,4.333333333,4.0,4.0,4.0,4.25,3.6,4.25,3.6,4.0,18,17,18,20,7,B,1,3,0,0,1,1,6,5,4,3,4,4,4,4,3,3,2,4,4,4,4,4,4,4,4,4,4,3,4,4,4,5,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,3,5,4,4,4,4,3,3,4,3,4,3,2,4,2,3,4,4,4,4,3,3,3,5.375,5,3.833333333,3.2,3.8,4.8,4.0,4.0,4.0,4.0,4.0,3.0,4.0,2.5,4,3,3,3,7.0,5.5,5.0,6.0,6.5,28,0,8.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,2,1,1,1,1,6,4,4,4,5,4,4,4,4,4,4,4,3,3,3,3,7,0,2,1,1,1,1,1,1,1,1,1,1,0,1,0,2,0,1,1,1,7,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,0.772727273,6.0,0.0,6.8855,-0.8855,7.827,6.805,5.738,6.831,7.225,0.8855,李珈瑶,3.0,"任务一：
“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
以“敦煌学”为“冷门绝学”为代表，AI加入后对于学科的赋能与伦理挑战
收集——AI识别和成立数据集
通过AI技术识别敦煌的文本与壁画影像，建立敦煌学的数据集，并根据学科需要进行符号拆分，形成最小单位的token。
展示——AI数字博物馆、多模态
基于敦煌学数据库，生成线上多模态虚拟博物馆对用户进行开放，并配以文字、视频及沉浸式展示，提供多模态全方位展示。
创新——文生图和文生视频的素材
基于敦煌学数据库，将AI数据集提供给其他的大数据模型，让其自动学习“敦煌学”的元素，在用户使用时，能够直接生成带有“敦煌风格”的视频、图片。
教育——AI教学
在“敦煌学”日常教学中，使用AI虚拟技术为同学们提供全方位的学习素材
周边产品——AI文创、3D打印、艺术风格
伦理——学科边界是否被打破？
当AI介入后宗教是否还能被信徒接受？",0.0003352206085781,0.2941176470588235,0.2121212121212121,0.2941176470588235,0.0535739284823322,0.3902664685404128,0.1878871768712997,0.2713178294573643,0.0516877637130801
132004,7,1307,协调员,4.0,29990.0,4.0,4.0,5.0,4.0,4.333333333333333,4.0,4.0,3.6666666666666665,3.6666666666666665,4.0,4.333333333,4.0,5.666666667,3.477777778,3.866666667,3.2,3.2,4.5,3.4,4.7,0.375,3.8,4.666666667,4.0,4.0,4.0,4.0,4.0,4.0,4.5,4.6,4.0,4.2,4.6,23,16,21,23,7,B,1,2,0,0,1,1,6,6,4,3,4,4,5,4,4,4,4,4,4,4,5,4,4,3,4,4,3,4,3,3,4,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4,3,4,3,4,3,4,4,3,4,3,2,3,2,3,2,3,3,4,4,4,4,3,2,4,6.0,6,4.0,4.0,3.4,3.8,4.0,4.0,4.0,3.5,3.75,3.333333333,2.666666667,2.5,4,3,2,4,5.0,2.0,4.5,5.0,5.5,21,0,9.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,1,1,1,1,1,1,1,1,9,5,3,4,4,4,4,5,4,4,4,3,1,4,3,4,9,1,1,1,1,0,2,1,1,1,1,1,1,1,1,1,2,1,2,1,2,8,5,3,4,4,5,5,4,4,4,4,3,2,4,3,4,0.909090909,4.4,1.6,5.2855,0.7145,5.827,3.305,5.238,5.831,6.225,0.7145,王晗,1.0,"任务一：
“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
以敦煌学为冷门学科代表，其在人工智能时代可能换发新生命力的原因：
敦煌学作为考古学科的一个重要分支其背后的社会和历史意义具有很大的研究价值。
随着新媒体时代的传播，“敦煌风”越来越收到大众关注，促使大众对其背后的深渊含义做出探索
可能面临的挑战：
AI创作可能带来伦理和宗教问题
AI创作带来的版权问题
应用于创新
数字博物馆
智能分类
二次创作
文创产品",5.554649709644199e-05,0.0769230769230769,0.0526315789473684,0.0769230769230769,0.0394069664902998,0.2287151017812974,0.1778678447008133,0.2925925925925925,0.0377643504531721
132021,7,1307,启发员,2.0,1020.0,2.0,2.0,4.333333333333333,5.666666666666667,4.0,5.0,5.333333333333333,4.333333333333333,4.333333333333333,5.0,6.0,4.666666667,5.666666667,3.917592593,4.505555556,4.033333333,4.2,4.4,3.9,4.6,0.25,4.2,5.0,4.0,4.4,4.333333333,4.0,4.5,4.666666667,5.0,4.8,3.75,4.4,5.0,24,15,22,25,7,B,1,1,0,0,1,1,7,8,4,2,3,5,4,4,4,5,4,3,4,5,5,5,4,5,5,5,4,4,5,5,5,5,5,5,5,4,4,5,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,5,4,2,5,3,3,5,3,2,2,5,5,4,2,3,6,7.625,8,3.666666667,4.0,4.6,5.0,4.4,5.0,4.833333333,5.0,5.0,3.666666667,4.666666667,2.75,4,2,3,6,5.5,3.5,4.0,4.0,4.0,22,1,7.5,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,1,1,1,1,1,1,7,5,3,4,4,5,4,5,4,5,4,4,3,4,4,5,8,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,2,1,1,7,5,3,4,5,5,5,4,4,4,5,4,4,4,4,5,0.818181818,4.2,2.8,5.0855,1.9145,6.327,4.805,4.738,4.831,4.725,1.9145,武一帆,2.0,"选择的“冷门绝学”是敦煌壁画建筑图像艺术的谱系构建与传承路径研究方向。通过AI赋能大致可以有以下几个方面的未来发展前景：
1.收集整理
通过AI进行敦煌壁画的扫描、汇总和整理，可以识别出具有同类型特点的数据和纹样并整合。
2.展示
利用AI进行敦煌壁画的特色展示
3.再创作
利用AI进行壁画的再创作
4.文创
可以利用AI进行有创意的文创
同时可能会出现的伦理问题：可能会对宗教进行冲击，再创作的内容不容易获得认同感。",2.517962709364285e-09,0.1525423728813559,0.1379310344827586,0.1525423728813559,0.0213603896310928,0.4085776968990794,0.1474678218364715,0.4418604651162791,0.0235294117647059
132002,8,1308,记录员,6.0,29989.0,4.0,4.0,2.6666666666666665,2.333333333333333,5.0,5.0,4.0,3.6666666666666665,3.333333333333333,4.333333333,3.666666667,3.666666667,5.333333333,3.131481481,3.788888889,3.733333333,3.4,4.0,3.7,4.5,0.375,3.4,5.0,4.333333333,4.0,4.666666667,4.0,4.5,4.333333333,4.75,3.8,3.75,3.8,3.8,19,15,19,19,8,B,1,3,0,0,1,1,6,7,3,2,4,5,4,4,2,3,3,3,4,4,4,5,4,3,4,4,4,2,2,4,4,3,4,4,3,5,4,2,4,5,3,3,4,4,4,2,4,3,3,4,4,3,3,2,2,3,2,3,1,1,3,3,2,4,4,5,4,3,5,5,6.625,7,3.666666667,3.0,3.2,3.6,4.0,3.6,4.0,3.0,3.5,2.0,3.0,1.75,4,3,5,5,6.0,4.5,4.5,4.5,5.5,22,1,8.0,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,2,1,1,1,1,1,1,1,2,8,5,3,4,4,5,5,4,3,4,4,5,2,3,3,4,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,2,0,1,8,5,3,5,5,5,5,4,4,3,2,4,2,3,4,4,0.909090909,5.0,1.0,5.8855,0.1145,6.827,5.805,5.238,5.331,6.225,0.1145,石航奇,1.0,"选择同声传译学
在人工智能时代可能焕发新生命力的可能原因
①传统的同声传译需要传译者先听然后根据内容做出翻译，然后读出来。AI的发展能够帮助其中的某一个步骤，甚至取代它。比如我们可以直接跳过传译者听的过程，直接让人工智能写出语言精彩又得体的传译稿，然后人工的任务量就大大降低。
②更极端一点，ai可以直接通过翻译读取发言人的话，通过大模型生成、优化出对应语言内容，然后通过类似元宇宙的模式将内容传递给听取发言的人们，传译者的任务可能只是在极短的时间内优化发言内容，使其更有人情味。
可能遇到的挑战
因为某些需要用到同声传译的场合都相对严肃严谨，同声传译的质量是一个很大的问题。以现有的技术，和对AI的展望来看，人工智能能否在一些语境下完美的完成任务，依旧是一个疑问。而且语言学存在的意义就是为了交流，人工智能取代这部分工作又是否符合大家对这个领域的认知，大众又是否能不能接受。",7.192578600891538e-06,0.1052631578947368,0.0727272727272727,0.1052631578947368,0.0307641126344265,0.3550608801143078,0.143334224820137,0.2956521739130435,0.0361771058315334
132011,8,1308,启发员,5.0,29994.0,4.0,4.0,3.0,5.0,4.666666666666667,4.666666666666667,3.333333333333333,3.333333333333333,3.333333333333333,5.0,5.0,4.333333333,5.0,4.057407407,4.344444444,4.066666667,3.4,4.9,4.4,4.8,0.5,4.4,5.0,4.666666667,4.2,4.666666667,4.666666667,4.5,5.0,5.0,4.0,4.0,5.0,5.0,20,16,25,25,8,B,1,1,0,0,1,1,7,6,2,3,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,3,4,3,4,4,4,4,3,3,4,4,4,4,3,4,6,6.375,6,3.333333333,4.0,3.8,4.0,4.0,4.0,4.0,4.0,4.0,3.0,4.0,3.5,4,3,4,6,5.0,4.0,4.5,4.5,5.5,26,1,7.0,1,1,1,1,1,1,1,1,0,2,1,1,1,1,1,1,0,2,1,2,0,2,1,1,7,5,4,5,5,5,4,5,4,4,4,4,3,4,3,3,10,1,1,1,1,1,2,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,5,4,5,5,5,5,5,4,5,4,4,2,4,3,3,0.772727273,4.7,2.3,5.5855,1.4145,5.827,5.305,5.238,5.331,6.225,1.4145,孙泽毅,3.0,"AI在同声传译学科的机遇与挑战
同声传译作为口译的分支，其难度大、门槛高一直是人类译员渴望又难以人人实现的理想。借助AI工具，人类译员可以将听—思—译的过程转变为看—思—译，即借助AI快速的语音识别和修订任务将音频转化为文字，帮助译员进行同声传译；
同时，AI工具的发展在某些程度也会取代人类译员，例如在非正式场合下，双方借助智能工具可以快速了解对方的意图并进行沟通，人类译者在此过程中消失，取而代之的是新的设备与技术。
在训练译员时，AI也可以作为一种翻译、语言的学习工具帮助学员掌握语言能力与翻译能力。
然而，AI的发展是否会挤压人类译员的生存空间依然是严峻的问题。例如许多翻译公司或者委托人会不断榨取译员的时间和价值，同时AI翻译的质量也需要有人类译员把关。",8.19274392060339e-13,0.0903225806451612,0.0784313725490195,0.0903225806451612,0.012541082857637,0.2509346490554111,0.1653453111648559,0.3872549019607843,0.0154417513682564
132023,8,1308,协调员,5.0,1021.0,4.0,4.0,2.0,3.0,4.666666666666667,4.666666666666667,3.0,2.333333333333333,3.0,5.0,5.0,4.333333333,4.0,3.060185185,3.361111111,3.166666667,3.0,3.2,3.7,4.3,0.5,3.4,4.333333333,4.0,3.4,3.333333333,3.0,4.0,4.0,4.0,3.4,3.0,4.2,3.4,17,12,21,17,8,B,1,2,0,0,1,1,7,6,4,3,4,4,4,4,4,3,4,4,4,4,4,4,4,3,3,3,4,3,2,3,4,4,3,3,4,4,4,3,4,4,4,3,3,3,4,3,4,3,4,3,3,3,3,4,3,4,2,4,3,2,4,3,3,2,4,4,3,3,2,3,6.375,6,3.833333333,3.8,3.0,3.6,3.8,3.4,3.666666667,3.5,3.0,3.333333333,4.0,2.5,3,3,2,3,5.5,3.0,4.5,4.5,5.0,20,0,7.0,0,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,0,2,1,1,7,4,2,3,4,3,3,4,4,2,3,4,3,2,3,4,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,7,4,4,4,4,4,5,3,3,4,3,4,2,2,3,2,0.727272727,4.5,2.5,5.3855,1.6145,6.327,4.305,5.238,5.331,5.725,1.6145,张译文,2.0,"在人工智能时代，我们认为同声传译学科可能会焕发新生，得到发展。
可能的原因是：
随着大语言模型的兴起，我们认为在同声传译中可以适当引进人工智能，对译者的工作起到辅助作用；
同声传译需要译者即时的专注倾听，如果引进人工智能，可以在一定程度上转化为视译，可以缓解译者的工作强度，鉴于我们认为翻译工作仍然需要译者人为的理解加入，所以我们希望人工智能技术的引用只是提供一定的参考；
达到多语种同声传译技术标准的翻译人才目前依然处于较匮乏的阶段，引入人工智能有助于同声传译的学科发展
未来挑战：
可能由于人工智能技术的功能太过强大和过多干预，导致同声传译人员需求降低，取代同声传译的人才，让其丧失意义。",2.152031588908486e-05,0.0,0.0,0.0,0.031906039027097,0.392172418386509,0.1261122822761535,0.3012048192771084,0.0414201183431952
132009,9,1309,记录员,3.0,29993.0,4.0,4.0,4.666666666666667,5.333333333333333,1.3333333333333333,3.333333333333333,4.666666666666667,2.333333333333333,2.333333333333333,2.666666667,3.333333333,3.666666667,5.666666667,4.539814815,4.238888889,4.433333333,3.6,4.8,3.5,4.0,0.375,3.8,4.0,4.333333333,3.8,3.666666667,4.333333333,4.0,4.666666667,3.75,3.0,2.75,3.4,4.0,15,11,17,20,9,B,1,3,0,0,0,1,7,5,5,4,3,5,4,3,4,3,3,4,5,3,4,3,4,5,3,4,5,3,4,3,4,5,5,5,4,5,5,4,5,4,5,4,5,5,4,5,4,5,4,3,4,5,4,5,4,4,3,4,2,2,4,1,4,4,3,4,4,4,3,4,5.75,5,4.0,3.8,3.8,4.6,4.6,4.6,3.666666667,4.5,4.0,4.333333333,4.0,2.0,4,4,3,4,6.0,5.0,5.0,5.5,5.0,19,0,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,0,2,0,2,0,1,1,1,7,4,5,4,4,4,3,4,3,3,4,5,3,3,2,2,8,1,1,1,1,0,2,0,2,1,1,1,1,1,2,0,1,1,1,1,1,7,4,5,4,4,4,4,4,3,3,4,5,3,2,2,3,0.590909091,5.3,1.7,6.1855,0.8145,6.827,6.305,5.738,6.331,5.725,0.8145,冯新语,,"任务二
考古学
原因：考古学实地挖掘动工成本大、难度大，对于施工技术要求高
可能挑战：需要大量原始数据支撑
	       可能被不法分子利用，进行数据投毒或“越狱攻击
	       需要处理多种因素作用下的复杂情况
传承：总结过往经验，把所有历史数据和实地图像模型云上传
社会应用：利用AI技术复原古代遗址的真实情况
                       运用AI构建虚拟现实的实地景象
创新：可以利用AI技术判断推测陵墓挖掘最佳位置
              利用AI识别文物推断其可能的朝代、用途
对于有关史实进行AI补充、推理
古代语言研究
原因：语言研究书目繁杂，研究时间成本高
可能挑战：部分语言实物资料欠缺或已经彻底失传
传承&创新：用AI来识别文本，通过不断比对推测可能的语法结构和语言体系
社会应用：可以用来拯救濒危语言
	       可以利用AI进行濒危语言的作品生成和创作，增强宣传力度",5.716048246558798e-05,0.1794871794871794,0.1578947368421052,0.1794871794871794,0.0547376029304916,0.7250659391749131,0.1514695584774017,0.4576923076923077,0.0507726269315673
132010,9,1309,协调员,7.0,29993.0,4.0,4.0,3.6666666666666665,3.333333333333333,4.0,5.333333333333333,4.333333333333333,1.6666666666666667,1.6666666666666667,4.666666667,3.666666667,5.0,4.333333333,3.510185185,4.061111111,3.366666667,2.2,4.3,4.4,4.8,0.125,3.0,3.0,4.333333333,3.0,3.0,4.0,3.5,3.666666667,3.75,2.6,3.25,3.8,3.0,13,13,19,15,9,B,1,2,0,0,0,1,8,6,4,4,4,4,3,3,2,2,4,2,4,4,4,4,4,4,3,4,4,3,3,3,3,3,3,3,3,4,4,3,4,4,3,3,3,3,3,4,3,3,3,3,3,3,3,3,3,4,3,4,2,2,4,2,2,4,3,3,3,2,3,4,6.75,6,3.666666667,2.8,3.4,3.0,3.8,3.0,3.833333333,3.25,3.0,2.666666667,4.0,2.25,3,2,3,4,4.5,4.5,4.5,4.0,5.0,19,0,6.0,1,1,1,1,1,1,1,1,0,2,1,1,1,2,0,1,1,1,1,2,1,2,1,2,6,4,5,3,3,3,3,4,3,4,2,2,4,2,2,1,8,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,8,4,5,4,3,3,3,4,3,4,2,2,2,2,2,1,0.818181818,4.5,3.5,5.3855,2.6145,5.327,5.805,5.238,4.831,5.725,2.6145,朱佳音,,"冷门绝学因AI焕发新生
1考古
AI可以帮助预测挖掘坟墓的地点、分析文物所属的文明，还原古人生活场景、构建供人进行VR体验的模型。风险是需要大量数据，可能被投毒
2古籍研究、古语言研究
AI可以帮助总结古籍内容、补全残缺内容、识别古文字，从而加深人们对古文字的理解，还可以撰写宣传文本帮助濒临失传的语言的生存。挑战是人们自己不了解的古语言文字，难以指导AI的分析。",1.6662546949532641e-09,0.1348314606741573,0.0919540229885057,0.1348314606741573,0.0183328697098315,0.3851322696048159,0.1447919309139251,0.3611111111111111,0.021311475409836
132016,9,1309,启发员,1.0,29994.0,1.0,1.0,5.333333333333333,4.666666666666667,5.0,3.0,1.6666666666666667,3.6666666666666665,3.6666666666666665,4.333333333,3.666666667,4.333333333,4.666666667,3.211111111,4.266666667,3.6,3.6,4.5,3.3,4.0,0.25,3.8,4.0,4.0,3.8,4.0,4.0,3.5,4.666666667,4.0,3.0,4.25,4.4,3.6,15,17,22,18,9,B,1,1,0,0,0,1,7,7,4,5,4,4,5,4,5,4,5,4,4,5,5,4,4,5,4,5,4,4,5,4,5,4,5,5,5,5,4,5,4,4,5,5,4,4,5,5,4,4,5,5,5,5,5,4,3,4,2,4,3,2,4,2,4,4,5,4,4,4,5,5,7.0,7,4.333333333,4.4,4.4,4.8,4.4,4.6,4.5,4.5,5.0,3.666666667,4.0,2.25,4,4,5,5,3.0,5.0,5.0,5.5,5.0,22,1,7.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,1,1,1,1,1,1,1,1,7,4,4,4,4,4,4,4,4,4,4,3,3,4,3,4,7,1,1,1,1,0,1,0,1,0,1,1,1,1,1,1,1,0,1,1,1,1,4,4,4,4,4,4,4,4,4,4,3,3,3,4,4,0.727272727,4.7,2.3,5.5855,1.4145,3.827,6.305,5.738,6.331,5.725,1.4145,张子豪,,"对考古发掘：综合各方信息，处理各类信号，如波、图片·、等，建立力学模型，给出合适的挖掘方案。
对古文字：识别、破译文字的内容，并对以往的解释给出修正。
其余：见冯新语同学。",8.747091378006876e-40,0.0,0.0,0.0,0.0039786290780948,0.4620374847321463,0.0578198097646236,0.4821428571428571,0.0055316533497233
131001,10,1310,记录员,5.0,29001.0,3.0,4.0,1.0,4.333333333333333,4.0,6.0,5.0,3.6666666666666665,3.6666666666666665,3.666666667,4.666666667,5.666666667,5.666666667,4.821296296,3.927777778,3.566666667,4.4,3.8,4.6,5.6,0.5,4.8,4.333333333,3.666666667,3.8,3.333333333,4.0,4.5,4.333333333,4.0,3.6,3.5,4.4,4.2,18,14,22,21,10,B,1,3,0,0,0,1,7,8,5,3,4,5,4,5,4,4,4,3,4,4,4,5,5,4,4,5,4,5,5,4,5,4,5,5,4,5,4,3,4,4,5,4,5,5,4,5,4,4,5,5,5,4,4,5,4,5,4,5,4,5,4,5,4,5,4,5,4,4,3,4,7.625,8,4.333333333,3.8,4.6,4.6,4.0,4.6,4.333333333,4.5,4.5,4.333333333,4.666666667,4.5,4,4,3,4,6.5,6.0,5.5,7.0,7.0,26,1,8.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,8,5,2,5,4,4,2,4,4,4,3,4,3,4,3,4,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,8,5,2,4,5,4,4,5,5,5,4,5,3,4,3,4,0.863636364,6.4,0.6,7.2855,-0.2855,7.327,7.305,6.238,7.831,7.725,0.2855,李钟洋,2.0,"冷门学科：古文字学
冷门（小众）的原因/特点：不符合现代社会的发展节奏但是对于人类学、人类起源有深远意义
利用AI的优点：
效率更高（快速比对数据库确定字的含义）
研究的方法更加多样化现代化（文字识别和图形识别的学科交叉）
AI可以利用多模态学习联系多学科的知识（相比于传统的研究更加多角度）
AI可以通过对语法的分析推导出未识别的字符
可以训练专门用于古文字学的专属模型，提升科研效率
利用AI进行文字演化规律建模
建立古文字学知识图谱
AI助力发展思路：
通过AI采用多形式多媒体的方式（如数字博物馆）
重新焕发古文字学的魅力，吸引公众了解和参与到其中，有助于传承创新和发展
建立数据化的数据库，有助于传播推广和研究
AI应用的挑战：
数据稀缺
文字识别复杂
AI识别的可靠性存在不确定性",1.3864344735678964e-05,0.2222222222222222,0.1999999999999999,0.2222222222222222,0.0383767883726959,0.4517245665173862,0.1209475547075271,0.3266331658291457,0.0411328388401888
132020,10,1310,启发员,6.0,1019.0,8.0,8.0,3.0,4.0,5.333333333333333,2.333333333333333,3.0,2.6666666666666665,1.6666666666666667,4.333333333,2.666666667,4.333333333,4.666666667,2.710185185,4.261111111,3.566666667,2.4,4.1,3.3,3.8,0.375,3.6,3.666666667,4.0,3.4,2.666666667,2.0,3.5,3.666666667,3.0,3.8,3.5,3.2,3.8,19,14,16,19,10,B,1,1,0,0,0,1,10,7,3,4,4,4,4,3,5,4,4,4,4,5,4,3,3,4,4,4,4,2,4,4,4,4,4,3,3,4,4,4,4,4,4,4,4,3,4,3,3,3,4,4,4,3,3,4,4,4,2,4,2,2,4,2,4,4,4,4,3,2,3,3,8.125,7,3.666666667,4.2,3.6,3.6,4.0,3.8,3.833333333,3.25,3.5,4.0,4.0,2.0,3,2,3,3,7.5,7.0,6.0,7.0,7.5,17,0,7.0,0,1,1,1,1,1,1,1,1,2,1,1,0,2,1,1,0,2,1,1,1,1,0,2,7,2,1,3,2,2,4,4,3,4,2,4,2,2,1,2,9,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,2,7,5,3,4,4,4,3,4,3,4,3,4,2,3,2,3,0.772727273,7.0,3.0,7.8855,2.1145,8.327,8.305,6.738,7.831,8.225,2.1145,李乔易,3.0,"冷门学科：古文字学
冷门（小众）的原因/特点：不符合现代社会的发展节奏但是对于人类学、人类起源有深远意义
利用AI的优点：
效率更高（快速比对数据库确定字的含义）
研究的方法更加多样化现代化（文字识别和图形识别的学科交叉）
AI可以利用多模态学习联系多学科的知识（相比于传统的研究更加多角度）
AI可以通过对语法的分析推导出未识别的字符
可以训练专门用于古文字学的专属模型，提升科研效率
利用AI进行文字演化规律建模
建立古文字学知识图谱
AI助力发展思路：
通过AI采用多形式多媒体的方式（如数字博物馆）
重新焕发古文字学的魅力，吸引公众了解和参与到其中，有助于传承创新和发展
建立数据化的数据库，有助于传播推广和研究
AI应用的挑战：
数据稀缺
文字识别复杂
AI识别的可靠性存在不确定性",6.398479221566035e-14,0.0758293838862559,0.0669856459330143,0.0758293838862559,0.0108790694837017,0.2176031453510675,0.1352373659610748,0.3969849246231156,0.0145167217934583
132022,10,1310,协调员,6.0,1020.0,4.0,4.0,5.333333333333333,4.0,5.0,3.0,4.666666666666667,2.333333333333333,2.333333333333333,5.666666667,3.333333333,5.333333333,5.0,2.935185185,2.611111111,2.666666667,3.0,2.6,3.3,4.6,0.5,4.0,3.666666667,2.666666667,2.8,4.0,2.333333333,3.5,3.333333333,2.0,3.4,3.25,2.6,4.2,17,13,13,21,10,B,1,2,0,0,0,1,8,7,4,3,3,4,3,4,3,4,3,3,4,2,5,2,5,4,4,2,3,5,3,3,3,5,3,5,3,5,3,5,3,5,3,4,2,4,4,3,5,3,2,5,4,3,5,4,3,4,2,5,4,3,3,3,4,4,3,5,4,4,4,4,7.375,7,3.5,3.4,3.2,3.8,4.2,3.4,3.666666667,3.25,4.25,3.666666667,4.0,3.0,4,4,4,4,7.5,7.0,6.0,7.0,7.5,20,1,8.0,0,1,1,2,1,2,1,1,1,1,1,1,0,1,0,1,0,1,0,2,0,2,0,1,5,3,1,3,4,4,4,3,3,2,3,3,2,3,2,2,8,0,1,1,1,0,1,0,1,0,2,1,1,1,1,0,2,0,2,1,1,8,4,1,3,4,4,3,5,4,3,4,4,3,3,2,2,0.409090909,7.0,1.0,7.8855,0.1145,8.327,8.305,6.738,7.831,8.225,0.1145,周鸿祥,1.0,"冷门学科：古文字学
冷门（小众）的原因/特点：不符合现代社会的发展节奏但是对于人类学、人类起源有深远意义
利用AI的优点：
效率更高（快速比对数据库确定字的含义）
研究的方法更加多样化现代化（文字识别和图形识别的学科交叉）
AI可以利用多模态学习联系多学科的知识（相比于传统的研究更加多角度）
AI可以通过对语法的分析推导出未识别的字符
可以训练专门用于古文字学的专属模型，提升科研效率
利用AI进行文字演化规律建模
建立古文字学知识图谱
AI助力发展思路：
通过AI采用多形式多媒体的方式（如数字博物馆）
重新焕发古文字学的魅力，吸引公众了解和参与到其中，有助于传承创新和发展
建立数据化的数据库，有助于传播推广和研究
AI应用的挑战：
数据稀缺
文字识别复杂
AI识别的可靠性存在不确定性",6.416305397077355e-10,0.123076923076923,0.109375,0.123076923076923,0.0158498700310657,0.4118245168217646,0.0904745534062385,0.3668341708542713,0.0203847258110824
132005,11,1311,协调员,6.0,29991.0,4.0,4.0,4.0,2.6666666666666665,5.0,4.0,3.0,2.0,2.0,3.666666667,3.666666667,3.666666667,3.666666667,3.535185185,4.211111111,4.266666667,3.6,3.5,3.4,4.0,0.125,4.2,4.666666667,3.333333333,4.4,4.333333333,3.0,2.5,4.333333333,4.0,3.8,3.0,3.8,3.8,19,12,19,19,11,B,1,2,0,0,0,1,7,7,4,3,4,4,4,4,3,2,4,4,4,4,4,4,4,3,4,4,4,4,3,5,3,3,3,4,3,4,4,3,4,4,3,3,3,4,4,3,4,2,4,4,3,3,3,4,5,4,4,3,3,2,3,2,4,4,4,3,4,1,3,4,7.0,7,3.833333333,3.4,4.0,3.2,3.8,3.4,3.833333333,3.25,3.25,4.333333333,3.333333333,2.75,4,1,3,4,7.0,7.0,6.0,7.5,7.5,23,1,7.0,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,1,1,1,1,0,1,1,1,6,4,2,3,4,5,4,4,4,5,5,4,3,2,2,2,6,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,6,4,2,4,4,5,5,5,4,4,4,4,4,2,2,2,0.772727273,7.0,0.0,7.8855,-0.8855,7.827,8.305,6.738,8.331,8.225,0.8855,王上,2.0,"学科：考古学 
 人工智能时代可能焕发新生命力的可能原因
利用人工智能的数据处理和分析能力：对考古资料进行分类整理，对新发现的数据进行快速分析。
利用其他的，如图像识别技术，总结分析过往已有的资料，对文物进行区分以及年代分析。
多模态学习：通过AI将其他学科比如生物、化学分析对考古具体活动提供支持。
文化传播：设立AI虚拟人物，比如建立专门的数据库后形成一个活的”知识库“，建立可亲可爱的知识传播员。
文化展览：通过AI技术对文物进行多方面展示；对于已经灭失的文物进行复刻，数据再现。
 未来发展中面临的可能挑战
海量数据可能会造成人工智能幻觉，可能会给出错误的考古知识或数据，甚至出现“不尊重”的伦理问题。
考古数据本身的确定性可能存疑，以此为依据的AI意见的真实性也存疑。",1.0221814346481208e-05,0.0941176470588235,0.072289156626506,0.0941176470588235,0.0387764809391448,0.4668167944148504,0.1861158162355423,0.3807106598984771,0.0436063641720683
132015,11,1311,记录员,6.0,29994.0,3.0,4.0,4.333333333333333,3.6666666666666665,5.0,4.0,3.0,3.0,2.6666666666666665,5.666666667,4.666666667,4.0,4.333333333,4.074074074,4.444444444,3.666666667,3.0,4.9,4.4,4.7,0.375,3.8,5.0,4.0,4.4,3.0,4.0,5.0,4.0,5.0,4.0,2.75,4.4,3.8,20,11,22,19,11,B,1,3,0,0,0,1,7,6,4,4,4,4,5,5,5,4,4,4,4,4,5,5,5,3,3,4,4,4,4,4,3,2,2,3,3,4,4,4,3,3,5,5,4,4,4,4,4,3,4,4,3,3,3,3,4,4,2,4,2,3,4,2,3,4,4,4,4,4,4,4,6.375,6,4.333333333,4.2,4.0,2.6,3.6,4.4,4.166666667,3.75,3.25,3.333333333,4.0,2.25,4,4,4,4,7.0,6.5,5.5,6.5,7.0,24,0,6.0,0,1,1,1,0,1,1,2,1,1,1,1,1,2,1,1,1,1,1,1,1,2,0,2,6,5,2,5,3,3,3,4,4,4,5,5,3,3,2,3,8,1,1,1,1,0,2,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,5,3,4,5,5,5,4,4,4,3,4,3,3,3,3,0.727272727,6.5,0.5,7.3855,-0.3855,7.827,7.805,6.238,7.331,7.725,0.3855,王梦珂,3.0,"一. 人工智能时代可能焕发新生命力的可能原因
人工智能可以在文物发掘、文物保护、文物展览、文化传播方面发挥作用
1.利用人工智能的数据处理和分析能力：对考古资料进行分类整理，对新发现的数据进行快速分析。
2.利用其他的，如图像识别技术，总结分析过往已有的资料，对文物进行区分以及年代分析。
3.多模态学习：通过AI将其他学科比如生物、化学分析对考古具体活动提供支持。
4.文化传播：设立AI虚拟人物，比如建立专门的数据库后形成一个活的”知识库“，建立可亲可爱的知识传播员。
5.文化展览：通过AI技术对文物进行多方面展示；对于已经灭失的文物进行复刻，数据再现。
二. 未来发展中面临的可能挑战
1.海量数据可能会造成人工智能幻觉，可能会给出错误的考古知识或数据，甚至出现“不尊重”的伦理问题。
2.考古数据本身的确定性可能存疑，以此为依据的AI意见的真实性也存疑。",6.591567784985206e-05,0.2531645569620253,0.1558441558441558,0.2531645569620253,0.0418225265919585,0.5420037470710777,0.1966050714254379,0.3849557522123893,0.0446236559139785
132019,11,1311,启发员,8.0,1019.0,9.0,9.0,2.6666666666666665,3.6666666666666665,3.0,1.6666666666666667,4.333333333333333,3.6666666666666665,3.333333333333333,5.0,5.0,3.333333333,4.666666667,3.423148148,3.538888889,2.233333333,2.4,3.6,2.8,3.7,0.25,3.4,3.666666667,3.333333333,3.6,3.333333333,2.666666667,3.5,3.666666667,3.75,3.6,3.75,3.4,3.8,18,15,17,19,11,B,1,1,0,0,0,1,7,6,4,4,3,4,4,4,3,3,4,2,4,4,5,5,4,3,4,4,3,4,4,3,4,3,3,3,3,5,4,3,4,4,3,3,3,3,5,4,4,3,3,4,3,4,4,3,3,4,3,3,2,2,4,2,2,3,4,4,4,3,4,5,6.375,6,3.833333333,3.2,3.6,3.2,4.0,3.4,4.166666667,3.5,3.75,2.666666667,3.666666667,2.25,4,3,4,5,7.5,6.0,6.0,7.0,7.0,19,0,6.0,1,1,1,1,1,1,1,2,0,2,1,1,1,2,1,2,1,1,0,2,0,1,1,2,5,3,2,3,3,3,4,3,4,4,4,3,4,3,3,4,7,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,2,0,2,1,1,7,4,3,3,4,4,3,3,3,4,4,3,2,3,4,4,0.772727273,6.7,0.3,7.5855,-0.5855,8.327,7.305,6.738,7.831,7.725,0.5855,曹颖,1.0,"学科：考古 古文字学 文物保护等
应用
在挖掘过程中利用AI探测位置
人工智能可以利用输入数据研究和分析甲骨文
图像识别技术 对文物进行区分以及年代分析
对考古资料进行分类整理解读 
与其他学科进行联系交融，如化学分析
通过AI再现文物全貌，复刻已经损坏的文物
在参观过程中个性化推送，机器人引导，激发人们保护意识
对古籍历史数据保存传承，如线上图书馆，增强实用性
挑战
AI数据过多产生“幻觉”，传播虚假信息错误引导
文化遗产被娱乐化，不够尊重
文创版权问题
考古数据正确性难以评估，容易造成偏差",1.2009733626612918e-16,0.0357142857142857,0.0240963855421686,0.0357142857142857,0.0088622754491017,0.3263658515462542,0.1238787621259689,0.4328358208955223,0.0125432525951557
132001,12,1312,启发员,4.0,29989.0,6.0,7.0,3.0,4.333333333333333,5.666666666666667,3.0,4.333333333333333,3.6666666666666665,4.0,5.666666667,5.666666667,4.0,4.333333333,3.959259259,3.755555556,3.533333333,3.2,4.1,2.8,3.9,0.625,4.4,4.666666667,3.666666667,4.4,5.0,4.0,4.5,4.333333333,4.0,4.4,4.5,4.4,4.4,22,18,22,22,12,B,1,1,0,0,0,1,7,7,4,4,5,4,5,5,4,5,5,4,5,4,5,5,4,3,5,5,4,4,4,5,4,4,4,3,4,4,3,4,4,4,4,4,5,5,5,5,4,4,4,3,4,4,4,4,3,4,2,4,3,2,4,3,4,4,3,4,2,3,4,6,7.0,7,4.5,4.6,4.4,3.8,3.8,4.6,4.333333333,4.25,3.75,3.666666667,4.0,2.5,2,3,4,6,6.0,4.0,4.5,5.5,6.0,23,0,7.0,1,1,1,1,1,1,1,2,1,1,1,1,1,1,0,1,1,1,1,2,0,1,1,1,7,4,4,4,5,5,5,5,4,5,4,4,4,4,4,4,7,0,1,1,2,0,1,0,1,1,1,1,1,1,1,1,2,0,2,0,1,6,4,3,4,4,5,5,4,4,4,5,5,4,4,4,3,0.681818182,5.2,1.8,6.0855,0.9145,6.827,5.305,5.238,6.331,6.725,0.9145,王璇,1.0,"敦煌学
AI助力敦煌学的发展：AI可以修复壁画；AI可以提取敦煌壁画的元素，比如人物的服饰、壁画中的器具等，然后根据还原的服饰。器具等模型，人们可以复原古代的用具，根据敦煌壁画做出的各种器具可以作为敦煌文创周边，服饰可以助于影视剧的拍摄等。这些都有利于敦煌学的发展。
面临的挑战：AI需要学习，但是懂得敦煌学的专家很少，所以AI的学习资料来源比较少；不同的AI修复壁画会有不同的结果，如何判断哪个结果是正确的，还需要建立专门的机构去鉴别。",9.166348438970662e-13,0.1043478260869565,0.0884955752212389,0.1043478260869565,0.0122645044799509,0.3081105554949815,0.113467626273632,0.4076923076923077,0.0163227594702802
132007,12,1312,协调员,3.0,29992.0,3.0,3.0,1.6666666666666667,3.0,4.666666666666667,4.333333333333333,4.666666666666667,4.0,2.6666666666666665,5.0,3.0,3.666666667,5.0,4.12037037,4.722222222,4.333333333,4.0,4.4,4.2,4.4,0.625,4.2,3.333333333,3.666666667,4.2,3.0,3.0,3.5,4.666666667,4.5,4.0,3.5,4.2,4.0,20,14,21,20,12,B,1,2,0,0,0,1,10,6,5,4,4,4,5,5,3,3,4,3,4,5,5,3,4,4,2,3,4,4,3,4,2,2,2,4,2,4,4,4,4,3,4,3,4,4,4,5,5,4,5,4,4,4,4,3,4,4,2,4,3,2,4,1,2,4,3,4,5,3,2,5,7.5,6,4.5,3.4,3.6,2.4,3.8,3.8,3.833333333,4.75,4.0,3.0,4.0,2.0,5,3,2,5,7.0,4.0,6.0,6.5,7.0,20,0,8.0,0,2,1,1,1,1,1,1,0,2,1,1,0,1,0,1,1,1,1,2,0,1,1,1,7,4,1,4,3,3,3,5,4,4,4,4,3,2,3,3,8,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,2,1,1,1,4,3,4,3,3,4,4,3,5,5,4,2,4,4,4,0.681818182,6.1,3.9,6.9855,3.0145,7.827,5.305,6.738,7.331,7.725,3.0145,陈佳祺,2.0,"冷门绝学：历史文物修复和推测（壁画修复）
AI焕发生命力：
AI给出修复建议：将一些有关文物修复的专业知识以及相关的史料等史学知识投喂给AI，AI可以不加主观的色彩进行模拟修复，进一步给出一些专业认识一些修复还原的建议，提交了修复的效率和成本；
AI落实自己执行：同时AI可以根据专家学者的设计修复图稿和要求对文物直接进行修复，这一部分AI是作为一个执行者去帮助专业人士进行文物的修复，减小了因为人为因素造成修复失败的可能；
根据壁画推测以前真实的历史情况：在这一部分，通过将扫描后的文物资料投喂给AI，AI可以根据壁画、文物透露出来的信息推测当时社会中人们的穿着打扮、风俗习惯等，并以图片、视频等可视化的形式呈现出来，帮助历史文化的还原与应用。
可能面临的挑战：
由于相关知识太过专业化，AI可能无法完全掌握这部分知识，同时不同学者、机构有其不同的学术偏好，将带有主观色彩的知识传递给AI，可能引发一些偏差与歧视，不利于还原文物的真实情况；
应用AI的建议：
设置专门的AI修复鉴别机构，减少主观意识的干扰；
人为地对AI修复结果进行鉴别，减小偏差。",0.0013435745926043,0.4827586206896552,0.4642857142857142,0.4827586206896552,0.0669717602410983,0.4588064815435883,0.1867309063673019,0.2518248175182482,0.066321243523316
132008,12,1312,记录员,16.0,29992.0,4.0,4.0,3.333333333333333,5.0,4.333333333333333,4.666666666666667,1.3333333333333333,4.666666666666667,4.666666666666667,4.666666667,6.0,5.0,5.0,4.562037037,4.372222222,4.233333333,3.4,5.6,4.7,4.4,0.375,4.0,4.666666667,4.333333333,3.8,4.666666667,3.666666667,4.0,5.0,4.5,3.6,4.25,3.2,4.6,18,17,16,23,12,B,1,3,0,0,0,1,8,8,5,4,5,4,5,4,5,4,5,2,4,5,5,5,4,5,4,5,4,5,5,5,5,5,4,4,4,4,5,5,4,5,4,5,4,5,4,5,4,3,5,5,4,4,4,4,5,4,1,5,2,1,5,1,3,4,4,4,5,2,3,5,8.0,8,4.5,4.0,4.8,4.4,4.6,4.4,4.666666667,4.25,4.25,4.0,4.666666667,1.25,5,2,3,5,6.0,3.5,5.0,4.5,5.0,18,1,8.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,1,8,4,3,4,5,5,4,4,5,4,2,4,2,4,5,5,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,8,5,4,4,5,4,5,3,4,5,3,5,2,4,5,5,0.863636364,4.8,3.2,5.6855,2.3145,6.827,4.805,5.738,5.331,5.725,2.3145,孙从然,3.0,"本组讨论了敦煌学在AI时代的发展
用途
我们可以通过AI技术修复和处理敦煌石窟中的一些损失的壁画和文物，比如使其完善，让它动起来，同时识别物品的年代和特点，生成相关模型，使其可以准确的被生产运用之现实场景和作品中
2．问题
   AI技术对文物的修复可能仅仅建立在美术层面，对于相应的历史文化和知识可能欠缺。同时，多种AI技术可能产生五花八门的修复结果，难以比较优劣。
3.解决方法
将AI修复技术作为辅助，让其先给出修复建议与方法，再由相关专业人员进行修改，然后再进行最终的修复。成立专门的修复筛选机构，集中专业人士力量，进行修复结果的最终筛选。
4.反思
  AI对于文物的修复在目前看来不能成为主流，但一定具有很强大的辅助作用，能更好的协助文物历史工作者在自己的领域做出伟大的贡献。",3.679844244525866e-16,0.0789473684210526,0.0707964601769911,0.0789473684210526,0.009701412094427,0.3997260905553041,0.184728056192398,0.4549763033175355,0.0133481646273637
141061,5,1405,0,7.0,10047.0,8.0,8.0,3.6666666666666665,3.0,5.0,4.0,3.0,3.0,3.6666666666666665,4.0,3.333333333,4.0,3.666666667,4.890740741,4.344444444,4.066666667,3.4,3.6,4.2,4.0,0.125,5.0,5.0,3.666666667,4.4,5.0,4.666666667,4.0,4.333333333,4.5,4.0,4.0,4.6,5.0,20,16,23,25,5,C,0,0,0,0,1,0,3,5,3,4,4,4,3,4,4,4,4,4,4,3,4,4,4,3,2,4,3,3,3,3,4,4,4,3,4,4,4,3,3,3,5,5,5,5,5,3,5,4,4,4,3,4,4,4,4,4,4,4,4,3,4,3,3,3,3,4,4,4,3,4,4.25,5,3.666666667,4.0,3.2,3.8,3.4,5.0,3.333333333,4.0,3.75,3.666666667,4.0,3.5,4,4,3,4,1.0,1.0,1.0,1.0,1.0,21,0,7.0,0,2,1,1,1,1,1,1,1,2,1,1,1,2,0,1,1,1,1,2,1,2,1,2,5,4,5,5,5,5,5,5,5,5,3,4,4,4,4,3,6,1,1,1,2,1,2,1,1,1,2,1,1,1,1,1,2,0,2,1,2,4,3,4,4,5,5,5,5,5,5,5,5,3,3,3,3,0.863636364,1.0,2.0,1.8855,1.1145,1.827,2.305,1.738,1.831,1.725,1.1145,郑丽萍,,"任务一：【冷门绝学】
翻译专业。思维链、举一反三、多模态学习、反思能力。
说明如何利用AI技术助力该学科的传承、创新与社会应用。",,,,,,,,,
142000,5,1405,0,5.0,10984.0,2.0,2.0,3.6666666666666665,2.6666666666666665,4.666666666666667,1.6666666666666667,4.0,2.0,2.0,4.0,2.666666667,2.333333333,3.0,3.002777778,4.016666667,3.1,2.6,3.1,2.1,2.4,0.375,3.2,4.0,4.0,3.8,3.333333333,3.0,4.0,3.666666667,3.75,2.4,3.0,3.0,4.0,12,12,15,20,5,C,0,0,0,0,1,0,6,8,4,4,4,4,4,4,5,4,5,4,4,4,4,5,3,4,4,4,4,3,3,4,4,4,4,4,4,4,5,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,2,4,2,3,2,2,4,2,2,2,4,4,4,3,3,5,7.25,8,4.0,4.4,3.6,4.0,4.4,5.0,4.0,5.0,5.0,2.333333333,3.666666667,2.0,4,3,3,5,3.0,2.0,3.5,1.0,1.0,19,0,7.0,0,1,1,2,1,2,1,2,1,2,0,2,0,2,1,1,0,2,1,1,0,2,0,2,4,4,2,3,3,3,4,4,3,3,4,5,4,2,2,2,6,1,2,1,2,1,2,0,2,1,2,1,2,1,2,0,2,1,2,1,2,5,5,3,4,4,4,4,2,4,3,3,4,2,2,2,2,0.636363636,2.1,3.9,2.9855,3.0145,3.827,3.305,4.238,1.831,1.725,3.0145,胡芳华,1.0,"任务一：【冷门绝学】
翻译专业 
文化差异
文化差异也是一个常见的翻译问题。 每个国家/地区都有重要的语言或俚语，如果翻译到另一个国家/地区可能会有不同的含义。
使用语言的区域越大，方言就越多，您可能会找到的口语词就越多——技术文件翻译、法律文件翻译或医学笔录翻译除外。
例如，英国人以干巴巴、尖刻的讽刺而闻名，这是他们幽默的标志。 然而，这种讽刺不仅在使用不同语言的国家可能不受欢迎，而且在其他使用英语的国家也可能不受欢迎。
无论如何，在人类中你必须注意。 在一种语言中可能是笑话的事情在另一种语言中可能是严厉的侮辱。 因此，您必须注意细微差别，以便意图在两种语言中都有效。 很快会有更多相关内容。
在利用人工智能时，应注意这些问题。在不同地区以及不同人群使用翻译时应做出不同应对方式。",1.8483771789416223e-05,0.0,0.0,0.0,0.0364375080613955,0.5201106743434948,0.1544055044651031,0.3023255813953488,0.0376692171865803
142002,5,1405,0,7.0,10986.0,4.0,4.0,3.333333333333333,3.333333333333333,5.0,3.6666666666666665,3.0,4.0,4.0,5.333333333,6.0,5.0,6.0,3.968518519,4.811111111,3.866666667,2.2,5.2,4.1,4.8,0.625,4.2,4.333333333,4.666666667,4.8,4.333333333,4.666666667,3.5,5.0,5.0,4.2,5.0,3.0,4.4,21,20,15,22,5,C,0,0,0,0,1,0,7,8,5,4,5,5,4,5,4,3,4,4,5,4,5,5,5,5,4,5,3,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,4,5,5,3,4,3,5,5,4,4,4,3,3,4,3,3,4,3,4,4,4,4,5,4,4,2,5,5,7.625,8,4.666666667,4.0,4.6,5.0,5.0,4.6,4.666666667,3.75,4.25,3.333333333,3.666666667,3.5,4,2,5,5,5.0,4.5,4.5,4.5,4.5,21,1,8.0,0,2,1,1,1,1,1,1,0,1,1,1,0,1,0,1,1,2,1,1,1,1,0,1,8,5,4,5,4,4,5,5,5,5,4,5,2,4,4,4,8,0,2,1,1,0,1,1,1,1,1,1,1,1,1,0,2,1,2,1,1,8,5,4,5,5,4,4,4,5,4,3,5,2,4,4,4,0.636363636,4.6,2.4,5.4855,1.5145,5.827,5.805,5.238,5.331,5.225,1.5145,惠瑞泽,3.0,"翻译：人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等）
翻译专业讲究的主要是信、达、雅三点，如何使用大模型的新技术去更好的完成这三点呢？
经查阅资料：常见的翻译问题和错误
我们认为这些问题亟待解决（并且也可以通过大模型来很好的完成）：
语言结构不同
保持语气和风格的一致性
语言和主题专业知识
常见的翻译问题之一是基本结构，即使是在基本词序层面也是如此。 
COT通过让模型逐步展示其推理过程，使得我们可以追踪其决策逻辑，这与符号推理中的逐步演绎有相似之处。",8.647178945634112e-12,0.0,0.0,0.0,0.0150130548302872,0.3882956684046394,0.1533824205398559,0.3783783783783784,0.0165289256198346
142003,6,1406,0,7.0,10986.0,5.0,5.0,2.0,4.333333333333333,4.333333333333333,2.333333333333333,4.0,4.0,2.6666666666666665,5.0,5.666666667,4.666666667,4.0,3.592592593,3.555555556,3.333333333,3.0,4.1,4.7,3.4,0.5,4.2,5.0,3.666666667,3.6,3.666666667,2.666666667,3.5,4.333333333,4.75,3.4,3.75,2.6,3.6,17,15,13,18,6,C,0,0,0,0,1,0,7,5,3,4,4,5,4,4,3,4,4,2,4,4,4,4,5,5,5,4,4,3,3,4,4,3,3,4,3,4,4,3,4,5,4,4,3,4,5,4,4,2,4,3,3,4,3,4,4,4,4,3,3,4,3,2,3,4,4,3,4,3,2,5,5.75,5,4.0,3.4,3.6,3.4,4.0,4.0,4.5,3.5,3.25,3.666666667,3.333333333,3.25,4,3,2,5,5.5,5.0,4.5,5.0,5.0,20,0,5.0,0,1,1,2,1,1,1,2,0,2,1,1,0,1,1,1,0,2,1,1,0,1,1,1,4,3,2,3,4,3,4,5,3,4,2,4,4,3,3,2,7,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,2,0,2,1,1,7,5,2,4,5,5,5,5,4,5,3,4,3,4,4,4,0.636363636,5.0,2.0,5.8855,1.1145,6.327,6.305,5.238,5.831,5.725,1.1145,贾珺同,2.0,"任务一：【冷门绝学】
例如考古，可以在人工智能时代有更大的突破。目前学生参与到的考古在实地是这样的模式：懂考古知识的实习生作为组长，带领10位左右当地的大爷大妈进行实地挖掘与监测。组长利用所学知识感受土地中土壤湿度、砂石厚度等判断是用怎样的挖掘工具以及挖掘精细地点，实践由大爷大妈进行。如果利用到人工智能，前期学习考古具体的考察经验，举一反三根据当地的天气环境、历史遗址进行进一步判断，代替组长的角色；之后发挥机器人的优势即使进入到炎夏或寒冬也不妨碍工作效率进行挖掘。即将面临的挑战如下：1、面对考古艰难环境、机械动作的难度较大（难以前进），需要比较大的技术突破；2、文物的价值很高，如果收到机械的损坏，代价比较高。",8.154182598805983e-08,0.0784313725490196,0.0408163265306122,0.0784313725490196,0.0229695372085855,0.5195329463405312,0.1109433621168136,0.3278688524590163,0.0253763440860215
142004,6,1406,0,3.0,10987.0,2.0,2.0,4.666666666666667,3.6666666666666665,5.666666666666667,2.6666666666666665,5.333333333333333,4.0,3.333333333333333,5.333333333,5.0,3.666666667,5.0,3.762037037,3.572222222,3.433333333,2.6,3.4,3.2,3.8,0.25,4.0,4.666666667,3.0,4.4,4.0,2.333333333,4.5,3.333333333,4.0,3.2,3.25,4.2,3.6,16,13,21,18,6,C,0,0,0,0,1,0,7,3,5,3,4,4,4,5,4,5,5,5,5,2,3,5,2,5,3,5,4,5,4,2,5,5,5,3,4,5,5,4,5,5,5,3,4,4,5,5,5,2,5,4,3,4,3,5,5,5,2,5,1,4,4,2,5,4,4,4,5,3,4,6,4.5,3,4.166666667,4.8,4.0,4.4,4.8,4.2,3.333333333,4.25,3.5,5.0,4.666666667,2.25,5,3,4,6,5.5,3.5,5.5,5.5,6.0,19,1,6.0,1,1,1,2,1,1,0,2,1,2,1,1,0,1,0,1,0,2,0,2,1,2,0,1,4,3,1,3,4,4,4,3,4,5,5,5,4,4,3,3,8,1,1,1,1,0,2,0,2,0,1,0,2,0,2,0,2,0,2,0,1,7,4,1,4,5,5,4,4,3,5,4,4,2,4,4,4,0.363636364,5.2,1.8,6.0855,0.9145,6.327,4.805,6.238,6.331,6.725,0.9145,李家骥,1.0,我认为文物考古需要人工智能的帮助，可以发挥更大潜力。例如通过人工智能将年代久远的资料和近代的资料相结合，通过算法分析来确定文物的大概位置，减少人力资源的使用。同时分析历史材料，分析出最好的挖掘方案，并且不会破坏文物。尽最大的可能保护文物。同时可以研究出专门的考古挖掘机器人。机器人可以代替真人去执行一些危险的高难度的任务。例如勘测山洞内的文物，勘探一些危险的墓穴，或者是一些狭小的空间，人类无法到达的。同时根据发掘地的土壤成分，分析挖掘的时间，挖掘的技巧。当文物出土🪨时，人工智能自动分析文物的材质，自动评判该用哪种保护材料来减缓文物的腐败。,1.9413927300026505e-05,0.0,0.0,0.0,0.0343792368777842,0.6045988032620395,0.182581752538681,0.3461538461538461,0.0469026548672566
142005,6,1406,0,4.0,10987.0,4.0,4.0,4.666666666666667,4.0,5.0,4.333333333333333,4.666666666666667,5.0,4.333333333333333,6.0,4.0,4.333333333,5.0,4.934259259,4.605555556,4.633333333,2.8,4.2,3.7,4.8,0.5,5.0,5.0,3.333333333,3.8,3.333333333,3.333333333,3.5,4.666666667,5.0,2.6,3.75,3.6,3.4,13,15,18,17,6,C,0,0,0,0,1,0,8,5,5,3,4,4,5,5,4,2,2,2,5,5,4,5,5,5,4,5,3,2,2,2,2,2,2,2,2,3,5,2,5,5,2,2,3,2,5,4,4,2,3,4,3,3,3,2,4,4,2,5,2,2,5,1,3,4,5,5,4,2,3,4,6.125,5,4.333333333,3.0,2.8,2.0,4.0,2.8,4.666666667,3.25,3.25,3.0,4.666666667,1.75,4,2,3,4,6.5,6.0,6.5,6.5,6.5,23,0,9.0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,9,4,2,4,4,3,3,4,4,4,4,3,3,5,4,4,9,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,2,0,2,1,1,8,4,2,4,5,5,5,5,5,5,5,5,2,5,5,5,0.863636364,6.4,1.6,7.2855,0.7145,7.327,7.305,7.238,7.331,7.225,0.7145,李沛杉,3.0,"分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
第六组讨论了考古学方面的AI的运用与挑战。
现阶段考古较多还是依靠人力去进行勘测挖掘，根据土壤情况判断此处是否会存在文物，依靠土壤情况判断是否存在文物这一环节可以依靠AI来进行，将观测到土壤的指标输入进模型，得到是否存在文物，并指导下一步是否应当在此处挖掘。除此之外，AI还能对文物进行数字化建模，展现其多模态学习的能力，使得文物能够更便捷的展示在大众面前，提升宣传力。对于考古机器人，AI也能在路径规划上指导机器人，寻找到更适合机器人前进的平坦路径。
但目前机器人仍不能在较为崎岖的路上行走，是将AI运用到路径规划上的挑战。除此之外，AI的运算需要消耗大量的电力，而考古所在的地方位置可能较为偏僻，因此供电也是AI面临的挑战。除此之外，AI+机器人是否能独立完成文物挖掘也是值得商讨的问题，他可能缺少人类的考古经验，从而对文物位置进行误判或甚至对文物造成损害。",8.975522937262767e-05,0.2432432432432432,0.2222222222222222,0.2432432432432432,0.048487642864662,0.6578047958185255,0.2086209207773208,0.3068965517241379,0.0462519936204146
142006,7,1407,0,4.0,10988.0,1.0,1.0,1.3333333333333333,3.6666666666666665,3.6666666666666665,2.6666666666666665,5.0,2.6666666666666665,3.333333333333333,5.333333333,4.666666667,3.666666667,6.0,2.761111111,2.566666667,2.4,2.4,2.5,3.4,4.1,0.25,4.2,4.333333333,3.0,4.4,4.666666667,3.333333333,4.0,3.666666667,4.75,3.4,3.75,3.6,4.8,17,15,18,24,7,C,0,0,0,0,1,0,7,9,5,4,5,4,4,5,5,4,4,3,3,4,5,5,5,5,5,5,4,1,1,1,3,3,1,1,1,4,4,5,4,4,3,5,3,3,4,5,4,5,5,4,3,4,4,4,4,4,1,4,1,1,5,3,4,4,4,4,4,1,3,4,8.25,9,4.5,3.8,2.4,1.8,4.2,3.6,4.833333333,4.75,3.75,4.0,4.333333333,1.5,4,1,3,4,7.0,7.0,5.5,5.5,6.0,21,1,8.0,1,1,1,1,1,1,1,1,1,2,1,1,0,2,1,1,1,2,1,1,1,1,1,1,8,4,2,4,4,5,5,5,5,4,4,4,3,4,3,3,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,1,1,8,4,2,3,4,5,4,5,4,5,3,4,2,3,2,3,0.909090909,6.2,0.8,7.0855,-0.0855,7.827,8.305,6.238,6.331,6.725,0.0855,李欣锐,1.0,"冷门绝学
“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
冷门学科：
语言学
利用AI技术助力该学科的传承、创新与社会应用：
可以利用多模态学习促进濒危语言的保护和复兴，整合语音、文字、图像与文化场景自动分析濒危语言的发音、语法以及语义特征。利用举一反三的能力进行对现有资料的分析，进行推断冷门与罕见字词的理解；利用反思能力持续更新算法与翻译能力，进行时间较长的自我更新以及自我迭代。
如今面临的问题为对于一些罕见语种或者方言，现有资料留存较少，并不能很好地进行分析与推断。同时，语言学的理论抽象性较强，而AI技术偏向于实用性，如何更好地实现学术与技术的融合是语言学学生以及AI本身在未来发展中的一大挑战。",,,,,,,,,
142007,7,1407,0,7.0,10988.0,2.0,2.0,3.333333333333333,4.0,4.333333333333333,4.666666666666667,3.6666666666666665,4.333333333333333,4.333333333333333,5.0,4.333333333,4.666666667,6.0,4.0,4.0,4.0,4.0,4.7,4.3,5.3,0.125,4.0,4.0,4.0,4.0,4.0,4.333333333,4.0,4.0,5.0,3.6,4.0,3.6,4.0,18,16,18,20,7,C,0,0,0,0,1,0,7,5,4,4,5,5,4,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,4,4,2,4,4,4,4,3,3,3,3,5.75,5,4.166666667,3.2,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,3.0,3,3,3,3,6.0,3.5,5.0,5.0,5.5,24,0,9.0,1,1,1,1,1,1,1,2,1,2,1,1,0,2,1,1,1,1,1,1,1,2,1,2,7,5,4,4,4,4,4,4,4,4,4,4,4,4,4,5,9,1,1,1,1,0,1,1,1,1,1,1,1,0,1,1,2,0,2,1,1,7,4,4,4,4,4,4,4,4,4,4,4,2,4,4,5,0.818181818,5.0,2.0,5.8855,1.1145,6.827,4.805,5.738,5.831,6.225,1.1145,刘晨,2.0,"问：“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
答：
请选择一门你认为具有更大发展潜力的冷门学科：小语种
分析其在人工智能时代可能焕发新生命力的可能原因：全球化发展与各国交流密切，人工智能时代可以弥补小语种精英人员人数的不足，并且线上使用可以操作的更为便捷，可以打破地理和时间的限制。
与其在未来发展中面临的可能挑战：可能遇到小语种当中生僻冷门的关键字，还有某些地区或者种族的方言时，人工智能无法保证能够做到十分准确，并且不同国家不同民族之间存在着不同的信仰和文化以及政治问题，可能会触及一些伦理或安全的红线。
如何利用AI技术助力该学科的传承、创新与社会应用：可以在短时间内广泛学习一个国家的历史文化起源，对于文化的传承具有很大的意义。还可以通过多种语言进行规律总结，找到不同语种间的规律。",0.0004654203130082,0.054054054054054,0.0277777777777777,0.054054054054054,0.0627835957311503,0.5089900705640527,0.1890475451946258,0.2758620689655172,0.0582341891045711
142008,7,1407,0,16.0,10989.0,4.0,6.0,2.333333333333333,4.333333333333333,2.333333333333333,5.666666666666667,6.0,2.333333333333333,3.6666666666666665,3.333333333,6.0,4.0,5.333333333,4.512037037,4.072222222,4.433333333,3.6,4.8,4.6,5.1,0.25,4.2,5.0,4.666666667,4.6,5.0,5.0,5.0,5.0,5.0,3.6,4.25,3.6,5.0,18,17,18,25,7,C,0,0,0,0,1,0,5,10,4,2,3,4,5,5,5,2,5,2,2,5,3,1,5,5,5,3,3,2,1,1,4,3,4,4,3,2,4,2,5,3,1,1,1,1,3,2,4,1,1,1,1,1,1,2,3,5,4,3,5,4,3,5,2,5,1,1,1,5,2,1,8.125,10,3.833333333,3.2,2.0,3.6,3.2,1.4,4.0,2.0,1.0,2.333333333,3.666666667,4.5,1,5,2,1,5.5,5.0,4.5,5.0,5.0,19,0,8.0,0,2,1,1,1,1,1,1,1,1,1,1,0,1,1,2,1,1,1,1,0,1,1,1,9,5,5,5,5,5,5,4,5,5,5,4,3,4,4,3,9,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,2,1,2,1,1,8,5,5,4,5,5,5,5,5,4,2,5,1,2,2,3,0.863636364,5.0,0.0,5.8855,-0.8855,6.327,6.305,5.238,5.831,5.725,0.8855,刘烨祁,3.0,"可以利用AI大语言模型，先输入高质量的甲骨文数据，再人工对齐，让AI记忆各个古文字对应的现代汉字翻译，训练AI对史料进行翻译，也可参考AI的判断结果破解新的古文字。
可能挑战：古文字的样本不够多，导致训练AI的数据不足。该学科受社会的关注度不够，参与AI训练的人员紧缺。古文字破解需要漫长的训练过程。对史料翻译的过程中可能有“幻觉”现象，产生不被社会伦理接纳的错误结果。同一个意义的文字可能有多种不同形式的变体，需要神经网络同时接受多个信息。古文字浩如烟海，计算成本高等。",6.10872963809103e-13,0.1290322580645161,0.1098901098901098,0.1290322580645161,0.0133945656333716,0.6335967219846445,0.1328934580087661,0.4592592592592592,0.0178725857595849
142009,8,1408,0,3.0,10989.0,1.0,1.0,2.6666666666666665,4.0,4.666666666666667,5.333333333333333,2.6666666666666665,3.0,3.0,5.0,4.333333333,5.666666667,4.666666667,4.363888889,4.183333333,4.1,4.6,4.7,4.1,4.5,0.25,5.0,5.0,4.666666667,5.0,5.0,4.666666667,4.0,5.0,5.0,5.0,5.0,5.0,5.0,25,20,25,25,8,C,0,0,0,0,1,0,6,6,3,4,4,3,4,4,4,5,5,4,5,5,5,4,4,5,4,4,4,4,3,3,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,4,5,5,5,5,4,3,2,4,4,4,4,5,3,4,3,5,5,4,3,3,3,6.0,6,3.666666667,4.6,3.6,4.0,4.4,5.0,4.5,4.75,5.0,3.666666667,3.666666667,3.75,4,3,3,3,6.0,4.0,5.0,6.0,5.0,20,1,7.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,8,5,4,5,5,5,5,5,5,5,5,5,2,3,3,3,8,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,5,4,5,5,5,5,5,5,5,5,5,3,3,3,3,0.772727273,5.2,0.8,6.0855,-0.0855,6.827,5.305,5.738,6.831,5.725,0.0855,罗润,1.0,"冷门学科：
古生物学
焕发新生命力的原因：
可以用AI进行场景再现、仿真模拟、进行古生物的形态预测、古生物蛋白重构甚至是克隆再现、构建生物宏观进化模型并不断根据新发现进行反馈修正。
可能面临什么挑战：
技术难题：可供参考的古生物数据集有限，研究的数据类型主要是岩石样本比较单一。
伦理难题：类似于克隆的问题。
助力：
宣传本学科，吸引学习兴趣，降低实验成本，加快实验进展。促进学科发展。有利于更快更准确地认识地球生命发展历程，帮助人类预测乃至于应对生物种类发展道路上存在的共性问题。",1.5086263897899015e-09,0.0298507462686567,0.0,0.0298507462686567,0.0174629784856105,0.4616843077753628,0.1390267312526703,0.362962962962963,0.0202446225221425
142010,8,1408,0,3.0,10990.0,0.0,0.0,3.333333333333333,3.0,4.0,2.333333333333333,4.0,3.333333333333333,3.6666666666666665,4.0,4.0,4.0,4.0,2.841666667,3.05,3.3,3.8,3.8,3.4,3.8,0.375,3.4,3.666666667,3.0,4.2,3.666666667,3.333333333,3.5,3.333333333,4.75,3.8,4.0,3.8,4.2,19,16,19,21,8,C,0,0,0,0,1,0,7,7,4,4,4,4,4,4,3,5,5,5,3,5,4,4,5,4,4,4,4,4,4,4,5,5,5,5,5,4,4,5,4,5,4,5,4,4,5,3,4,3,4,4,4,4,4,3,4,4,2,4,2,3,4,2,2,3,4,4,4,4,3,5,7.0,7,4.0,4.2,4.0,5.0,4.4,4.4,4.333333333,3.5,4.0,3.0,4.0,2.25,4,4,3,5,6.0,4.0,5.0,6.0,5.5,22,1,7.0,0,2,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,7,4,3,3,4,4,3,4,4,4,5,4,2,3,4,4,7,1,1,1,1,0,1,1,1,1,1,1,1,0,1,0,2,0,2,0,2,6,3,3,3,3,4,4,3,3,4,4,3,2,3,3,4,0.681818182,5.3,1.7,6.1855,0.8145,6.827,5.305,5.738,6.831,6.225,0.8145,吕祚琛,3.0,"我们组讨论目前我们看来的冷门专业：古生物学
针对其在人工智能时代
发展潜力：
1.我们首先想到的是对其宣传，可借助AI工具模拟其在线状态，作为视频图片，用于宣传我们。
2.了解到，古生物学包括的一个主要任务是生物宏观进化模型，在这方面我们可以利用AI技术做一个模拟预测，通过生物结构进化使用AI技术，预测在生物发展这么长的阶段，我们还没明确发现的生物其结构。
3.也可以关注到某些具体生物族群在进化或者某些过程中所遇到的问题，同时进行古生物结构预测，对目前尚未明确的某些生物帮助其分类与学习。
面临挑战：
1.技术问题：首先如果利用AI的话，就我们目前来看，首先实验数据是很重要的，但在这方面可能由于研究人员以及研究方法所需数据较难获得，所以需要在数据方面多下功夫。
2.伦理问题是值得考虑的问题。",0.000278087162079,0.1904761904761904,0.05,0.1904761904761904,0.0462351518525299,0.555568025530019,0.1812706887722015,0.3047619047619048,0.0504
142011,8,1408,0,4.0,10990.0,1.0,1.0,2.6666666666666665,2.0,6.0,5.0,5.0,2.6666666666666665,3.6666666666666665,6.0,6.0,5.666666667,6.0,4.618518519,4.711111111,4.266666667,4.6,4.3,4.5,4.2,0.25,4.2,5.0,4.0,3.6,4.666666667,3.0,4.0,4.0,4.0,4.2,4.25,4.4,4.6,21,17,22,23,8,C,0,0,0,0,1,0,6,7,3,4,5,5,5,5,5,4,5,5,4,5,5,5,4,4,4,4,4,5,4,4,4,5,5,5,5,4,4,3,3,4,5,5,4,5,5,4,5,5,4,4,5,4,5,4,5,5,1,5,5,2,5,1,5,5,5,5,5,5,4,6,6.625,7,4.5,4.6,4.2,4.8,3.6,4.8,4.5,4.5,4.5,4.666666667,5.0,2.25,5,5,4,6,7.0,7.0,6.0,6.5,6.5,20,1,6.0,1,2,1,2,0,2,1,1,1,1,1,1,0,1,0,2,1,1,0,2,1,1,1,1,4,4,3,2,5,5,4,4,4,3,3,4,4,4,3,4,8,1,1,1,1,0,1,1,1,1,2,1,1,0,1,1,2,1,1,0,2,6,5,3,4,5,5,5,4,4,4,4,5,4,2,4,2,0.681818182,6.6,-0.6,7.4855,-1.4855,7.827,8.305,6.738,7.331,7.225,1.4855,彭侃,2.0,"选择学科：古生物学
其在人工智能时代可能焕发新生命力的可能原因;
1.古生物学目前大众知名度较低，许多人对其了解并不足够且真实，可能产生对该学科错误的判断；AI的发展给文化的传播形式提供了更多的可能，如虚拟现实、仿真模拟等手段，让古生物进入大家的视野，使古生物学焕发生机。
其在未来发展中面临的可能挑战：
1.真实实验材料较少，AI如何真实的模拟实验材料
2.古生物学涉及知识面广，AI如何能动将其结合
3.实际应用问题：实际应用方面较少是古生物冷门的重要原因之一，AI能否发现该学科更多的应用范围
4.伦理问题
如何利用AI技术助力该学科的传承、创新与社会应用：
虚拟现实，带大众走进古代场景，更直观的认识古生物
古生物学 AI 研究，涵盖了微观和宏观化石分类、图像分割和预测等主要任务。 这些研究采用了广泛的技术，例如基于知识的系统（KBS）、神经网络、迁移学习和许多其他机器学习方法，可实现各种古生物学研究工作流程的自动化。 在这里，我们讨论他们的方法、数据集和性能，并将它们与更传统的人工智能研究进行比较。",0.0042414849844052,0.4615384615384615,0.0833333333333333,0.4615384615384615,0.0971504345536422,0.5498148208995933,0.1670786142349243,0.1963636363636363,0.0797872340425531
142012,9,1409,0,2.0,10991.0,1.0,1.0,2.6666666666666665,3.333333333333333,6.0,5.0,1.0,4.0,4.0,4.666666667,3.666666667,5.333333333,6.0,4.000925926,4.005555556,4.033333333,3.2,4.0,4.6,4.3,0.5,4.8,5.0,4.666666667,4.4,5.0,4.666666667,5.0,4.666666667,5.0,5.0,4.5,4.6,4.8,25,18,23,24,9,C,0,0,0,0,0,0,8,8,5,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,3,4,4,5,5,4,4,5,5,4,5,5,5,5,5,4,4,4,4,4,4,4,4,4,4,5,5,5,1,5,2,2,5,2,5,4,5,5,4,4,4,5,8.0,8,4.666666667,4.0,3.8,4.4,4.8,4.6,3.833333333,4.0,4.0,5.0,5.0,1.75,4,4,4,5,3.5,3.0,4.0,4.5,4.5,26,1,8.0,0,1,1,1,1,1,1,1,0,2,1,1,0,1,1,1,1,1,1,2,0,1,1,1,8,5,4,5,5,5,5,5,4,4,4,5,3,4,4,4,8,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,9,5,4,5,5,5,5,5,5,5,5,4,3,4,4,4,0.681818182,3.9,4.1,4.7855,3.2145,4.327,4.305,4.738,5.331,5.225,3.2145,齐保辉,3.0,我认为在通用人工智能时代，随着技术的发展和知识体系的变革，考古学和宗教学这些较为冷门的学科有望焕发新的生命力。首先，在当今社会，学生为了自己的职业发展，选择理工类学科作为自己的大学专业是极为普遍的现象，导致考古学和宗教学这些冷门学科逐渐淡出人们的视野，但是这些学科对于我们理解当今社会是十分必要的。在通用人工智能时代，我们可以利用人工智能的优势，让人工智能通过大数据不断地去学习这些学科的专业知识，不断助力学科发展，通过AI手段让这些学科在社会中不断地传承，应用。当然，我们也要时刻提防AI生成虚假信息的可能性。,,,,,,,,,
142013,9,1409,0,5.0,10991.0,2.0,2.0,2.333333333333333,2.333333333333333,5.0,5.666666666666667,5.0,2.6666666666666665,3.0,4.666666667,4.333333333,4.0,5.333333333,4.449074074,4.694444444,4.166666667,4.0,4.4,4.1,5.0,0.125,4.0,4.0,3.666666667,3.6,4.666666667,3.666666667,4.0,4.333333333,5.0,3.8,4.0,3.4,5.0,19,16,17,25,9,C,0,0,0,0,0,0,6,7,5,4,4,5,5,5,4,4,4,4,4,4,4,5,5,3,4,3,3,3,3,4,3,3,3,3,3,4,4,4,4,5,4,3,3,3,3,3,3,3,3,2,3,3,3,3,3,3,4,3,4,3,3,4,3,3,3,3,3,2,4,2,6.625,7,4.666666667,4.0,3.2,3.0,4.2,3.2,4.166666667,3.0,2.75,3.0,3.0,3.75,3,2,4,2,3.0,2.0,4.0,4.5,4.0,22,0,9.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,8,4,4,3,4,5,5,4,4,3,3,4,2,3,3,3,9,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,9,4,4,3,4,4,4,4,4,4,3,5,1,3,3,2,0.772727273,3.5,2.5,4.3855,1.6145,3.827,3.305,4.738,5.331,4.725,1.6145,齐巧霞,2.0,"请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
我认为在人工智能时代，古生物学将成为有更大发展潜力的冷门学科，古生物学之所以冷门，是因为从事这个领域学习研究的人较少，这个领域对是在探索人类未知的过去，而非未来，其经济价值和现实价值不大，人们学习这个学科的学习成本与从事这个领域的未来收益不成正比，因此这个学科不被人们所待见，这个学科的传承和发展存在很大的问题。随着ai技术的发展，由于ai学习知识和技术是没有成本的，ai可以做到助力该学科的传承、将该学科的相关知识以科普的形式展现给大众，让人类了解地球的过去。但我们不认为ai可以助力该学科的创新，毕竟这个学科的发展要基于大量的考古调查等需要人类细致去完成的工作。",,,,,,,,,
142014,9,1409,0,3.0,10991.0,5.0,5.0,3.6666666666666665,4.666666666666667,3.6666666666666665,3.333333333333333,3.6666666666666665,3.333333333333333,3.6666666666666665,5.0,5.0,5.0,4.0,3.997222222,3.983333333,3.9,3.4,4.5,4.0,4.5,0.375,4.0,4.0,3.666666667,4.2,4.0,4.0,3.0,3.333333333,3.25,3.4,3.5,3.2,3.8,17,14,16,19,9,C,0,0,0,0,0,0,6,6,3,4,4,3,4,4,3,3,4,4,3,3,4,4,4,4,4,4,4,3,3,3,4,4,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,4,3,3,3,3,3,3,4,3,4,4,3,4,4,3,3,4,2,3,2,6.0,6,3.666666667,3.4,3.4,3.4,4.0,4.0,3.833333333,3.75,3.25,3.333333333,3.666666667,3.25,4,2,3,2,5.0,3.0,5.0,4.5,4.5,20,0,5.0,0,1,1,1,1,1,1,2,1,1,0,2,0,1,1,1,0,2,0,2,0,1,0,2,6,4,3,5,4,4,4,4,4,4,5,4,4,3,4,4,7,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,0,2,1,1,7,4,3,4,4,4,4,4,4,4,4,4,2,3,3,4,0.545454545,4.4,1.6,5.2855,0.7145,5.827,4.305,5.738,5.331,5.225,0.7145,石翘楚,1.0,"我认为园林设计专业可以在人工智能时代焕发出色彩。首先，目前的园林设计专业只有少数的几个学校有，且目前社会中对此类人才的需求也较少，所以比较冷门。
人工智能兴起后，随着人们对社会现代化的要求越来越高，审美升级，对社会建筑风格和外观设计的要求也越来越高，且当下老破小地区偏多，未来注定会迎来比较大的革新建筑期。人工智能以其强大的整合能力和收集了世界各地建筑风格、美景的综合素质，可以给园林设计专业人才带来更多的灵感，帮助其结合本国要求，创造出适合中国现代化的新中式风格建筑，帮助城市焕然一新。",,,,,,,,,
142015,10,1410,0,10.0,10992.0,4.0,5.0,2.6666666666666665,5.0,3.6666666666666665,3.6666666666666665,2.6666666666666665,3.6666666666666665,3.333333333333333,6.0,5.0,4.666666667,5.0,3.322222222,3.933333333,3.6,2.6,5.1,4.3,4.7,0.375,4.4,4.666666667,4.333333333,3.8,4.333333333,4.0,4.5,4.666666667,5.0,3.2,4.5,4.4,4.6,16,18,22,23,10,C,0,0,0,0,0,0,8,7,5,5,5,5,5,3,5,4,5,4,4,5,4,5,5,5,4,5,4,2,2,2,3,3,3,3,2,4,5,4,5,5,2,2,2,2,4,4,4,2,3,3,4,4,3,3,4,4,2,4,2,2,4,2,2,4,4,5,5,2,3,6,7.375,7,4.666666667,4.4,3.0,2.8,4.6,2.4,4.666666667,3.25,3.5,3.0,4.0,2.0,5,2,3,6,7.0,4.5,6.5,7.0,7.5,19,0,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0,1,0,2,0,1,1,1,8,5,3,4,4,4,5,3,4,4,3,5,3,4,2,4,9,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,8,5,3,5,5,5,4,5,5,4,3,5,1,4,3,4,0.636363636,6.5,1.5,7.3855,0.6145,7.827,5.805,7.238,7.831,8.225,0.6145,孙嘉阳,1.0,"任务二：“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
1.选择的冷们学科：园林园艺
2.焕发新生命力的可能原因：AI可以在短时间内输出更多的可能性，效率高；人需要有大量的知识储备，学习过程长，而AI可以通过大数据直接学习，耗费的时间成本少；AI可以监控地区气候和天气变化，去匹配最适合的植物。
3.面临的挑战：植物是否真的可以实地应用，面临成本以及是否灭绝的问题；设计园林的方案对于园林工人难处理，比如设计很多林木占比，而不是灌木对于园林工人更加难以长期培育；AI创造出来的图纸还是太AI化，能被一眼看出不是真人设计的；AI不了解中国的风水问题，不具备玄学类的知识，特殊情况只能靠中国风水学专业的人来布景。
4.如何利用AI技术助力该学科传承、创新与社会应用：简易学习的难度，因为园林需要大量的知识网络，学习周期长，成果慢，AI可以加速该学科的学习成果，让学生更注重于结果的输出；创新方面，AI可以设计出更多元化的园艺造景图，效率比人类高，在短时间内更容易有创新发展；社会应用方面，AI技术可以缩短园林设计时间，并且能给社会带来较高收益（例如，布景植物的光合作用值更高，可以减少城市的热岛效应，改善城市空气质量和生态环境）。",0.0022467645276132,0.5084745762711864,0.4912280701754385,0.4406779661016949,0.0673820769527106,0.6558459915408041,0.1737945824861526,0.2505446623093681,0.0615471485036702
142016,10,1410,0,6.0,10992.0,3.0,3.0,3.0,1.0,2.0,1.6666666666666667,4.666666666666667,2.0,2.333333333333333,4.0,5.333333333,2.0,2.333333333,3.425,2.55,3.3,2.8,2.9,2.3,3.4,0.375,2.6,4.0,3.666666667,2.8,4.0,4.0,5.0,4.666666667,5.0,2.4,3.75,4.2,3.8,12,15,21,19,10,C,0,0,0,0,0,0,5,5,3,3,3,3,3,2,3,3,3,3,3,2,4,3,4,4,4,3,3,3,3,3,3,3,3,4,4,4,4,3,3,4,4,4,3,3,3,2,2,3,3,3,3,3,3,3,2,4,3,4,4,4,4,4,4,3,4,4,4,3,2,3,5.0,5,2.833333333,3.0,3.0,3.4,3.6,3.4,3.5,2.5,3.0,3.0,4.0,3.75,4,3,2,3,6.0,5.5,5.5,6.0,5.0,23,0,6.0,1,1,1,1,1,1,1,1,1,2,1,1,1,2,1,2,1,1,1,2,1,2,0,2,6,4,4,4,4,4,4,4,3,3,2,2,3,2,2,3,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,6,4,3,4,4,4,4,3,3,3,2,2,3,2,2,2,0.909090909,5.6,-0.6,6.4855,-1.4855,6.827,6.805,6.238,6.831,5.725,1.4855,孙雯,2.0,"冷门专业：园林园艺
AI的作用：具备有大量的知识储备，可以根据模板设计花圃，举一反三，迅速推荐大量可应用到实际的花卉植物，并且ai还可以结合当地天气土壤等多场景进行综合分析，并且可以在短暂的时间内获得大量的设计原型
挑战：专业化不够，适用大规模布景，对于细节的把控，实际应用上虚报率较高，更加细节的把控仍然需要真人专家进行，ai的创新性仍然不够，更多是基于节点进行模仿，原创能力较弱。园林园艺较大可能涉及到周易风水等知识，这些知识更多地需要基于实践进行创作应用",4.946304906946091e-06,0.2068965517241379,0.1481481481481481,0.2068965517241379,0.0307873368056268,0.3799690735511517,0.1126272603869438,0.3014705882352941,0.0331914893617021
142017,10,1410,0,5.0,10016.0,4.0,5.0,3.333333333333333,5.0,4.666666666666667,4.0,2.6666666666666665,5.0,4.666666666666667,5.0,4.666666667,5.666666667,5.333333333,4.058333333,4.35,4.1,3.6,4.4,3.7,4.2,0.625,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,4.8,5.0,25,20,24,25,10,C,0,0,0,0,0,0,7,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,3,2,4,6.375,6,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,3,3,2,4,6.5,4.5,6.0,6.0,6.5,19,1,9.0,0,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,0,2,1,1,1,1,1,2,7,5,5,5,5,5,5,5,5,5,5,5,2,4,5,5,9,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,1,1,1,9,5,5,5,5,5,5,5,5,5,5,5,2,5,5,5,0.727272727,5.9,1.1,6.7855,0.2145,7.327,5.805,6.738,6.831,7.225,0.2145,孙小宝,3.0,"园林园艺
园林园艺这一学科目前在我国的应用程度不足，并且相关专业的人才需要经过多年的浸润、大量的实地观察学习、广泛的了解才能有一定的理论基础。但是园林园艺人才在实践时往往要结合具体问题具体分析。这就使得普适性的理论知识和特定化的具体需求之间形成明显的矛盾。由于AI背后有巨大的知识储备，并且能够迅速、高效地分析比对当地的环境、甲方的需求以及自身具备的相关知识，对于典型案例进行综合迁移，使得其时间成本大大缩短，有助于解决人才培养周期长，成效慢导致该行业难以快速发展的尴尬现状。
同时，AI在领域的挑战也比较明显。由于AI只是基于数据库对于现状给出解决方案，所以，相对的，关于方案中植物的可获取度、珍稀度的考虑可能并不充分。对于园林景观后期的修缮难度评估亦面临难题。
基于第一段而言，AI对于该领域知识的把握、理解能够加速应用人才培养速度，促进传承。又因为其可以给学生提供大量参考案例、资料，也能促进创新。对于应用传承方面，AI可以帮助更好地结合植物特性、场景特点，基于不同的目的给出最佳方案",,,,,,,,,
142018,11,1411,0,5.0,10017.0,3.0,3.0,3.0,3.6666666666666665,5.0,4.666666666666667,4.333333333333333,4.0,4.0,5.0,4.666666667,4.0,4.0,3.80462963,3.827777778,3.966666667,3.8,4.4,3.7,4.0,0.625,4.0,4.333333333,3.666666667,3.8,4.666666667,3.333333333,4.0,4.666666667,5.0,3.2,4.25,4.0,3.6,16,17,20,18,11,C,0,0,0,0,0,0,7,7,4,4,4,4,4,3,4,3,4,4,4,4,4,5,5,4,4,4,4,3,4,4,3,4,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,4,4,4,4,4,4,3,4,4,4,4,3,4,4,4,4,3,1,3,5,7.0,7,3.833333333,3.8,3.8,3.6,4.0,4.0,4.333333333,4.0,4.25,4.0,4.0,3.5,3,1,3,5,5.0,4.5,5.0,5.5,4.5,27,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1,8,4,3,3,5,5,4,4,4,3,4,4,2,4,4,4,8,1,1,1,1,0,1,1,1,1,1,1,1,0,1,1,2,1,1,1,1,7,4,3,4,4,5,4,4,4,4,4,4,2,4,4,4,0.772727273,4.9,2.1,5.7855,1.2145,5.827,5.805,5.738,6.331,5.225,1.2145,唐功政,2.0,"使用AI助力甲骨文识别研究
通过使用甲骨文数据进行AI的自监督预训练，然后将带有标签的甲骨文数据放入模型进行有监督微调，从而提高模型对甲骨文的识别能力，最后参考专家意见进行人类反馈增强，进一步提高模型的能力，从而助力甲骨文识别的研究，发现和研究新的甲骨文字符，推动古文字研究的创新。并且可以开放虚拟博物馆，提高社会应用，",6.634547978977384e-39,0.0213903743315508,0.0108108108108108,0.0213903743315508,0.0078485268934511,0.3059419950350879,0.109937660396099,0.9540229885057472,0.010747118995209
142019,11,1411,0,4.0,10017.0,5.0,6.0,2.333333333333333,3.333333333333333,6.0,5.333333333333333,4.666666666666667,4.0,3.6666666666666665,5.0,6.0,5.333333333,5.666666667,4.091666667,3.55,4.3,3.8,4.1,4.2,5.2,0.25,4.4,4.333333333,3.666666667,4.0,4.333333333,2.666666667,4.5,4.333333333,4.5,3.4,3.25,3.0,4.6,17,13,15,23,11,C,0,0,0,0,0,0,4,8,4,5,5,5,4,5,4,3,5,4,3,2,5,5,4,3,5,3,3,2,2,2,4,4,3,2,3,4,4,4,2,3,4,3,4,3,4,2,4,3,4,3,5,4,3,5,3,5,4,4,3,4,4,3,4,3,4,3,4,3,4,2,6.5,8,4.666666667,3.8,2.4,3.2,3.4,3.6,4.0,3.25,3.75,4.0,4.333333333,3.5,4,3,4,2,3.5,4.0,4.0,4.0,4.0,22,1,7.0,0,1,1,2,1,1,1,2,1,2,1,1,1,2,0,2,0,2,1,1,1,2,0,2,6,3,2,3,4,4,5,3,4,5,5,3,3,4,3,4,8,1,1,1,2,0,1,0,1,1,1,1,1,1,1,0,2,0,2,0,2,7,5,2,4,4,4,5,5,4,5,5,3,2,4,4,4,0.590909091,3.9,0.1,4.7855,-0.7855,4.327,5.305,4.738,4.831,4.725,0.7855,王思其,1.0,"我认为具有更大发展潜力的冷门学科是心理陪伴，
像是虚拟“女友”在人工智能时代焕发出很新的生命力，但也面临着巨大的挑战，如对人的心理分析，以及对心理情绪的引导，对话时的语气等；
增强大语言模型算力，提高对语言分析的准确性，以及语言表达能力，动画表达能力，同时增强道德伦理的对齐，提高使用者对人工智能的满意度。",7.300328361327324e-09,0.0,0.0,0.0,0.0190911132739387,0.5603626938650855,0.1643448770046234,0.3956043956043956,0.0244233378561736
142020,11,1411,0,4.0,10018.0,3.0,4.0,4.333333333333333,5.0,5.0,5.333333333333333,5.666666666666667,2.333333333333333,2.333333333333333,6.0,5.666666667,5.333333333,6.0,4.59537037,4.572222222,4.433333333,3.6,3.9,4.2,4.6,0.125,4.8,4.666666667,3.333333333,4.8,5.0,4.666666667,3.5,4.333333333,4.25,3.2,3.0,4.4,4.6,16,12,22,23,11,C,0,0,0,0,0,0,6,9,5,5,5,5,5,5,4,4,5,5,5,5,5,5,5,5,5,4,5,5,5,4,5,5,5,5,5,4,4,4,4,4,4,5,5,5,5,4,5,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,4,2,5,7.875,9,5.0,4.6,4.6,5.0,4.0,4.8,5.0,4.5,5.0,5.0,5.0,5.0,4,4,2,5,3.0,3.0,3.5,3.0,3.5,21,0,6.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,0,1,8,4,5,5,5,5,5,5,5,5,5,4,2,2,2,3,8,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,8,4,3,3,4,5,5,5,5,5,5,4,3,2,2,3,0.863636364,3.2,2.8,4.0855,1.9145,3.827,4.305,4.238,3.831,4.225,1.9145,王文雅,3.0,"AI初步诊断精神类疾病
比如，官方机构给出权威自评量表，设置好相应参数，部分复杂的评估结果可以由AI直接得出，帮助初步确定或排除疾病的存在，节省掉大量时间和去医院做检查的精神成本
进一步，可以直接从患者自述发育史的文字中提取出可用于评估的关键信息，并给出具体分析结果、如得分、图表等",,,,,,,,,
141059,12,1412,0,2.0,10045.0,4.0,5.0,4.0,5.333333333333333,6.0,5.333333333333333,2.0,4.333333333333333,4.666666666666667,4.333333333,4.666666667,5.0,5.666666667,4.440740741,3.644444444,3.866666667,4.2,4.8,5.5,5.0,0.625,4.6,4.666666667,4.333333333,4.6,4.666666667,3.666666667,4.5,4.666666667,4.5,4.6,4.75,4.2,4.2,23,19,21,21,12,C,0,0,0,0,0,0,8,8,4,3,4,4,4,3,4,4,4,4,4,4,5,5,3,3,4,4,4,3,4,4,4,5,5,5,5,4,4,4,4,4,5,4,4,3,4,4,4,4,4,4,4,5,5,5,5,5,2,5,4,4,4,3,4,3,4,4,4,3,3,4,8.0,8,3.666666667,4.0,3.8,4.8,4.0,4.0,4.0,4.0,4.5,4.666666667,4.666666667,3.25,4,3,3,4,3.0,5.0,5.0,5.0,5.5,24,0,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,1,1,0,2,0,2,1,1,7,4,3,4,5,4,5,5,5,5,4,4,2,4,5,5,7,1,1,1,1,0,1,0,1,1,1,1,2,0,2,0,2,0,2,1,2,6,5,4,4,5,5,4,4,5,5,5,4,3,4,5,4,0.590909091,4.7,3.3,5.5855,2.4145,3.827,6.305,5.738,5.831,6.225,2.4145,赵江媛,1.0,"冷门绝学任务的讨论结果
小组成员重点讨论了植物学和考古学这两方面的冷门小众方向，对于植物学方向，可以利用AI搜集有关植物的知识，利用AI的思维链和反思能力；对于考古方向，可以使用大量的古物来训练AI，利用AI的举一反三和多模态学习的能力，使得AI可以实现多任务的学习。其实总结来说，冷门方向并不是知识冷门，只是应用场景少、任务需求低，可以完全利用大模型的多模态学习能力，将生活中常见的问题与冷门问题联系起来，这样AI就可以在冷门方向里发挥巨大的作用，推进冷门方向的发展。",,,,,,,,,
142021,12,1412,0,4.0,10018.0,2.0,2.0,1.3333333333333333,1.3333333333333333,4.333333333333333,4.333333333333333,6.0,2.333333333333333,2.6666666666666665,5.0,3.333333333,3.666666667,4.666666667,4.273148148,4.638888889,3.833333333,3.0,3.7,3.0,5.1,0.125,3.6,4.0,2.666666667,4.2,3.0,3.0,4.5,3.333333333,4.25,3.8,4.0,2.8,4.2,19,16,14,21,12,C,0,0,0,0,0,0,7,8,3,4,5,5,5,5,5,5,5,5,5,2,5,4,4,5,2,4,3,3,2,2,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,4,3,4,5,5,4,4,4,3,2,1,4,1,1,4,1,2,3,3,3,4,2,3,4,7.625,8,4.5,5.0,2.8,5.0,5.0,5.0,3.666666667,3.5,4.5,3.0,3.333333333,1.0,4,2,3,4,2.5,3.5,3.5,4.5,4.0,20,0,7.0,0,1,1,1,1,1,1,2,1,1,1,1,0,2,0,1,1,2,1,1,0,1,1,1,7,4,1,4,3,4,2,5,5,2,5,4,2,2,3,3,7,1,1,1,1,0,2,1,1,1,1,1,1,1,1,0,2,1,2,1,1,8,4,1,3,4,5,3,4,5,2,4,3,3,2,2,3,0.727272727,3.6,3.4,4.4855,2.5145,3.327,4.805,4.238,5.331,4.725,2.5145,吴子仪,3.0,"1.考古学：利用AI将获取到的考古材料上的古文字进行破译。由于同一朝代和时期的文字书写形式大致相同，可以将大量已知的古文字和破译后的相应文字输入，作为信息源，让AI从中总结出规律，进而能够对未破译的古文字材料进行翻译和解读。提高了人类阅读和理解古文字的效率。
2.植物学与中草药：利用AI阅读载有关于中草药的药效、使用方法及相关内容的古籍，整理归纳，提升人类对药用植物的使用的认知。
3.自动化：AI查看交通实况，检测交通系统中存在的漏洞。",7.26197434449759e-10,0.5217391304347825,0.2857142857142857,0.4347826086956521,0.0169204737732656,0.5373204913459011,0.130966231226921,0.3565891472868217,0.0201401050788091
142022,12,1412,0,3.0,10019.0,4.0,5.0,4.0,3.333333333333333,3.6666666666666665,4.333333333333333,3.333333333333333,3.0,3.0,3.666666667,4.0,4.0,4.0,3.922222222,3.533333333,3.2,3.2,3.9,4.6,5.5,0.0,3.4,3.666666667,3.333333333,3.2,2.666666667,3.0,4.0,3.666666667,3.5,3.4,3.5,4.4,3.6,17,14,22,18,12,C,0,0,0,0,0,0,7,9,4,3,3,4,4,4,3,4,4,3,4,4,4,5,4,5,3,4,4,5,5,5,5,5,5,5,4,5,4,5,4,5,5,5,5,5,5,5,4,5,4,5,5,5,5,5,4,4,4,5,5,5,4,5,4,4,4,4,3,4,3,4,8.25,9,3.666666667,3.6,4.6,4.8,4.6,5.0,4.166666667,4.5,5.0,4.333333333,4.333333333,4.75,3,4,3,4,2.5,3.0,3.5,6.0,4.5,24,0,7.0,1,1,0,2,0,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,8,3,3,3,1,4,3,4,4,4,2,2,2,3,3,3,7,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,7,4,3,3,4,4,3,4,4,4,3,2,2,3,3,3,0.772727273,3.9,3.1,4.7855,2.2145,3.327,4.305,4.238,6.831,5.225,2.2145,杨畅,2.0,"小众学科：考古学
信息来源，前两年的北京大学入学仅一人的考古学专业在新闻上有很久，对于考古资料，中国有很多相关的照片/资料，虽然说目前考古学冷门的一定原因在于很多人对其的薪资待遇以及劳累程度有着一定的失望，但是对于其中对于信息检索的部分，可以进行一定的机器学习进行特征学习。便于其中的年代、信息的辨别。
小众学科：人文植物学
信息来源：同组同学老师的研究方向，类似于人文和植物学、中药学的一个交叉方向，可以采取相关的知识库进行语料融合，对其中的一些未发现的交叉点进行预测，类似于生物学未知结构预测类似的情况。",,,,,,,,,
152000,5,1505,0,8.0,19986.0,6.0,7.0,3.333333333333333,5.0,3.6666666666666665,3.333333333333333,2.0,3.0,2.333333333333333,5.0,4.333333333,5.0,4.333333333,3.827777778,3.966666667,3.8,2.8,5.0,5.0,5.1,0.375,3.8,5.0,4.0,4.2,5.0,4.666666667,4.0,4.0,4.75,3.8,4.0,3.2,3.2,19,16,16,16,5,D,0,0,0,0,1,1,8,8,5,5,5,5,5,2,2,2,3,3,4,5,4,4,4,4,4,4,4,4,4,4,3,2,2,3,3,5,5,5,5,5,3,3,3,3,3,4,5,4,5,5,4,4,4,2,2,4,1,4,1,1,4,1,2,4,4,4,4,4,2,5,8.0,8,4.5,2.8,4.0,2.6,5.0,3.0,4.166666667,4.5,4.25,2.0,4.0,1.0,4,4,2,5,4.5,2.5,4.0,3.5,4.0,25,1,10.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,10,5,5,4,5,5,5,5,5,2,4,5,3,2,2,3,10,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,9,4,4,4,5,5,5,5,5,2,3,4,1,3,3,3,0.863636364,3.7,4.3,4.5855,3.4145,5.327,3.805,4.738,4.331,4.725,3.4145,李博深,1.0,,,,,,,,,,
152001,5,1505,0,6.0,19986.0,2.0,2.0,1.3333333333333333,6.0,4.0,3.0,5.666666666666667,1.6666666666666667,3.6666666666666665,5.666666667,5.666666667,4.333333333,5.666666667,4.546296296,3.277777778,3.666666667,2.0,5.6,2.9,4.4,0.0,4.8,4.0,2.666666667,4.8,3.333333333,3.333333333,3.0,3.0,4.0,1.8,3.0,3.2,2.4,9,12,16,12,5,D,0,0,0,0,1,1,7,1,5,1,5,2,5,1,5,5,5,1,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,4,4,5,5,5,4,4,5,5,5,5,5,5,5,5,3,3,4,1,4,1,1,3,1,1,3,5,5,5,1,5,5,3.25,1,3.166666667,4.2,5.0,5.0,4.4,4.6,5.0,5.0,5.0,2.333333333,3.666666667,1.0,5,1,5,5,2.5,2.5,3.5,3.0,2.5,19,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,1,9,3,2,5,5,3,2,5,5,5,5,4,1,5,1,5,8,0,1,1,1,1,1,1,1,1,2,1,1,1,1,0,2,0,1,1,1,7,3,1,4,5,5,2,5,5,5,4,5,1,2,1,2,0.727272727,2.8,4.2,3.6855,3.3145,3.327,3.805,4.238,3.831,3.225,3.3145,李佳祎,3.0,"任务一：【冷门绝学】
“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
我认为思想史（尤其是纸张出现之后的思想史）是一门能在人工智能时代有更大发展潜力的冷门学科。思想史这门学科的研究对象是思想界和社会公众的思想在复杂的政治、经济、社会等环境中变化的历史，该学科的研究人员往往需要阅读巨量的当时时代的文本才能了解当时的思想情况，对人的阅读量、记忆力等方面提出了极高的要求。而人工智能在这些方面的表现远强于人类，甚至可以利用多模态能力为思想史研究带来全新的视角。将人工智能应用于思想史研究可以为我们带来许多之前不曾取得的研究成果。",0.000768345265869,0.0454545454545454,0.0,0.0454545454545454,0.0647109577221742,0.3527724868421013,0.1382432729005813,0.2093023255813953,0.0610599078341014
152002,5,1505,0,6.0,19987.0,4.0,4.0,3.0,4.333333333333333,5.333333333333333,3.333333333333333,4.333333333333333,4.333333333333333,2.6666666666666665,4.0,4.333333333,5.0,5.0,3.521296296,3.127777778,3.766666667,2.6,4.6,4.9,4.8,0.25,3.6,3.333333333,2.666666667,4.2,3.0,3.0,3.5,3.666666667,3.25,3.4,4.0,3.8,3.4,17,16,19,17,5,D,0,0,0,0,1,1,7,8,5,5,5,5,5,4,4,4,4,4,5,4,4,4,4,5,4,4,4,3,3,4,4,5,5,5,4,4,5,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,3,2,4,5,2,3,2,3,4,4,5,5,4,4,5,7.625,8,4.833333333,4.2,3.6,4.6,4.6,5.0,4.166666667,5.0,5.0,4.0,3.333333333,2.75,5,4,4,5,6.0,5.0,5.0,5.5,6.5,24,0,7.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,0,1,1,2,1,1,8,3,3,3,2,3,4,4,4,4,5,4,3,3,2,3,7,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,7,3,3,2,3,3,4,2,4,4,4,4,2,4,4,5,0.772727273,5.6,1.4,6.4855,0.5145,6.827,6.305,5.738,6.331,7.225,0.5145,李珊,2.0,"冷门绝学：考古。
现存问题：专业人数少，传承存在问题；在发掘时存在重复简单的体力工作，项目时间长；在文物评判上，即使是专家也存在观点不一致的情况。
AI的传承：可以将现存的考古方法、知识等以文字和视频的形式录入专用人工智能中并开源。方便普通人学习
AI的应用：也可以为后来学习考古学的人提供参考信息；可以结合机器狗等载体结合AI模型开展简单的发掘工作代替人力；可以在发掘文物时或专家出现冲突时利用AI作为中立第三方判断其属性（如朝代）。
AI的创新：由于AI具有不同领域的知识，有可能将不同领域的专业同考古学结合，在这个过程中有可能产生交叉学科的创新想法；另外在考古过程中可能会涉及一些化学方法等，AI也有可能提供新的更适用廉价的方法。",5.531406307624758e-07,0.1573033707865168,0.1379310344827586,0.1573033707865168,0.0268055758794579,0.3675387988331626,0.1611436009407043,0.3333333333333333,0.028635346756152
152003,6,1506,0,5.0,19987.0,5.0,6.0,2.0,4.0,5.333333333333333,4.666666666666667,4.333333333333333,4.0,3.333333333333333,4.333333333,5.0,5.0,5.333333333,4.168518519,4.011111111,4.066666667,4.4,4.6,4.4,4.5,0.5,4.0,3.666666667,3.666666667,4.2,3.333333333,3.666666667,4.0,3.666666667,4.0,3.8,4.25,3.4,3.6,19,17,17,18,6,D,0,0,0,0,1,1,7,8,4,2,4,4,3,3,4,4,4,3,4,3,4,4,5,3,3,4,4,3,3,4,4,3,4,3,3,4,5,4,4,4,5,4,5,4,5,3,4,4,4,5,4,4,4,3,3,4,2,4,3,2,4,2,3,4,4,4,4,3,4,6,7.625,8,3.333333333,3.8,3.6,3.4,4.2,4.6,3.666666667,3.75,4.25,3.0,4.0,2.25,4,3,4,6,8.5,8.5,8.0,8.5,8.5,22,0,7.0,1,1,1,1,1,1,1,1,1,1,0,1,0,2,1,2,1,2,0,2,1,2,1,2,8,4,3,4,3,3,4,4,4,5,4,4,4,4,3,3,8,1,2,1,1,0,1,1,2,1,1,1,1,1,1,0,2,0,2,1,1,7,4,3,4,4,3,4,4,4,4,5,3,3,4,4,4,0.727272727,8.4,-1.4,9.2855,-2.2855,9.327,9.805,8.738,9.331,9.225,2.2855,林佳凝,3.0,"注：内容为讨论分工生成，同组内容整合成一版，上交三份
一 在人工智能时代可能焕发新生命力的可能原因
古生物学遇冷的原因有很多：1，需要学习的知识内容复杂，培养时间和周期长
2.就业遇冷，不符合大部分家长预期
3.国内资源不够丰富，学术垄断和学术壁垒存在
根据搜索结果，古生物学的主要研究内容为“保存在地层中的地质历史时期的生物遗体和遗迹化石”，古生物学需要学习的内容十分复杂，包括且不限于生物学、数学、物理学、地质学、信息技术等，而这些可以在ai的帮助下进行更加系统和针对的学习，制定个性化学习计划来保证在有限的时间内达成所需专业知识学习。
古生物学具体的研究目标包括“系统掌握化石的采集、处理、观察，化石及其保存信息的获取与表达（照相、描述与统计），以及地层剖面的测制与描述等的基本技能与方法。”和“掌握该专业所需的数学、物理学、化学、地理学等的基本内容以及必要的信息技术，能够获取、加工和应用古生物学及相关信息。”其中对于古生物学考察内容的识别和整理可以用到目前的ai技术，对于大量的图像进行识别，帮助考察和评判研究，我们可以将这些数据喂给ai，从而帮助我们识别和判断一些古生物，同时可以将研究成果转换为线上数据库，提高研究效率和资源共享。一些信息技术的工作也可以交给ai来进行，节省学习时间和成本。
而在ai存在的同时，古生物学的学科划分可以更加细化，例如古生物学信息管理和古生物自动识别等跨学科跨门类专业出现会拓展对人才的需要，增加就业岗位。
ai的出现解决了大部分古生物学面临的问题，可以使其焕发出新的生机活力。
二 挑战
1、受到化石文物数量以及保存完整程度上的限制，在数据规模上,现有的古生物训练数据集远远不及主流数据集，在利用大模型进行根据化石图像进行鉴定识别训练时可能面临训练集不够多，导致训练准确度不够高，这是为保证古生物学这门学科带有历史真实性不可避免的挑战。
同时如果模型参数过大，再利用ai大模型对古文物进行复原是可能会出现幻觉现象，从而影响复原的可靠性。/2、由于古生物学中的人工智能起步较晚，与主流研究在数据集规模上存在大约20年的差距,在算法上存在大约10年的差距，技术上面临巨大挑战。
数据模态数量的快速增长和复杂性使得数据处理变得繁琐且不一致，同时也缺乏明确的基准来评估数据收集和生成以及不同方法在类似任务上的性能。
三 如何利用AI技术助力该学科的传承、创新与社会应用
目前的AI技术在古生物学中主要是应用在自动化过程的改进，古生物学家希望通过构建更大规模的训练数据集与移植前沿算法，获得更高效的古生物学的人工智能模型。分类任务，图像识别、图像分割、预测，定量化犯法、数据驱动的研究模式，生命科学与地球科学共同的趋势。
首先，研究团队需要对数据集进行准备、标注、模型训练，有针对性地研发适合于画师图像分类的算法，用于所训练的模型，实现提高识别准确率的目的（主要还是提高自动化识别化石高分辨结构的一种方式）；对于破坏结构的岩石的3D结构的恢复，计算机断层扫描。
其次，古生物图像的计算分析即计算机视觉分析，可用于动植物研究、微生物进化和特定时期生物栖息环境的模拟，生物进化的研究不仅仅只是生物分子方面的分子钟。
最后，AI可以为古生物学研究赋能，创造更多的社会应用价值，解决一些经济活动领域的问题，比如石油勘探，为确定勘探点的潜力，并最大限度的减少采油过程中的相关费用。
传承与创新的意义，现在的古生物学家只需要对处理结果进行评估，对提取得到的微化石进行分类。",0.0010542335069391,0.2479338842975206,0.1848739495798319,0.2479338842975206,0.0701778632637316,0.4981103355742509,0.1540772169828415,0.2418772563176895,0.0658620689655172
152004,6,1506,0,3.0,19987.0,3.0,3.0,2.0,3.0,4.0,4.0,2.0,3.0,3.0,4.333333333,3.666666667,4.0,4.333333333,3.494444444,3.966666667,3.8,3.8,4.1,3.8,4.6,0.25,3.8,4.0,3.333333333,3.8,4.0,3.333333333,4.0,4.0,4.0,3.2,3.5,3.4,3.6,16,14,17,18,6,D,0,0,0,0,1,1,6,7,3,2,4,3,4,4,4,3,3,4,4,4,4,4,3,4,3,3,3,3,4,2,2,2,2,3,2,4,4,4,4,4,2,2,2,3,4,3,3,2,3,3,3,3,3,4,4,3,2,3,3,3,3,2,4,4,3,3,3,3,3,4,6.625,7,3.333333333,3.6,3.0,2.2,4.0,2.6,3.666666667,2.75,3.0,4.0,3.0,2.5,3,3,3,4,8.5,8.5,8.0,8.5,8.5,19,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,1,9,4,2,4,4,4,4,4,4,4,4,3,2,3,3,3,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,9,4,2,4,4,4,4,4,4,4,4,3,1,3,3,3,0.863636364,8.4,-2.4,9.2855,-3.2855,9.327,9.805,8.738,9.331,9.225,3.2855,林鑫涛,1.0,"ai赋能古生物学发展
一 在人工智能时代可能焕发新生命力的可能原因
古生物学遇冷的原因有很多：1，需要学习的知识内容复杂，培养时间和周期长
2.就业遇冷，不符合大部分家长预期
3.国内资源不够丰富，学术垄断和学术壁垒存在
根据搜索结果，古生物学的主要研究内容为“保存在地层中的地质历史时期的生物遗体和遗迹化石”，古生物学需要学习的内容十分复杂，包括且不限于生物学、数学、物理学、地质学、信息技术等，而这些可以在ai的帮助下进行更加系统和针对的学习，制定个性化学习计划来保证在有限的时间内达成所需专业知识学习。
古生物学具体的研究目标包括“系统掌握化石的采集、处理、观察，化石及其保存信息的获取与表达（照相、描述与统计），以及地层剖面的测制与描述等的基本技能与方法。”和“掌握该专业所需的数学、物理学、化学、地理学等的基本内容以及必要的信息技术，能够获取、加工和应用古生物学及相关信息。”其中对于古生物学考察内容的识别和整理可以用到目前的ai技术，对于大量的图像进行识别，帮助考察和评判研究，我们可以将这些数据喂给ai，从而帮助我们识别和判断一些古生物，同时可以将研究成果转换为线上数据库，提高研究效率和资源共享。一些信息技术的工作也可以交给ai来进行，节省学习时间和成本。
而在ai存在的同时，古生物学的学科划分可以更加细化，例如古生物学信息管理和古生物自动识别等跨学科跨门类专业出现会拓展对人才的需要，增加就业岗位。
ai的出现解决了大部分古生物学面临的问题，可以使其焕发出新的生机活力。
二 挑战
受到化石文物数量以及保存完整程度上的限制，在数据规模上,现有的古生物训练数据集远远不及主流数据集，在利用大模型进行根据化石图像进行鉴定识别训练时可能面临训练集不够多，导致训练准确度不够高，这是为保证古生物学这门学科带有历史真实性不可避免的挑战。
同时如果模型参数过大，再利用ai大模型对古文物进行复原是可能会出现幻觉现象，从而影响复原的可靠性。
由于古生物学中的人工智能起步较晚，与主流研究在数据集规模上存在大约20年的差距,在算法上存在大约10年的差距，技术上面临巨大挑战。
数据模态数量的快速增长和复杂性使得数据处理变得繁琐且不一致，同时也缺乏明确的基准来评估数据收集和生成以及不同方法在类似任务上的性能。
三 如何利用AI技术助力该学科的传承、创新与社会应用
目前的AI技术在古生物学中主要是应用在自动化过程的改进，古生物学家希望通过构建更大规模的训练数据集与移植前沿算法，获得更高效的古生物学的人工智能模型。分类任务，图像识别、图像分割、预测，定量化犯法、数据驱动的研究模式，生命科学与地球科学共同的趋势。
研究团队需要对数据集进行准备、标注、模型训练，有针对性地研发适合于画师图像分类的算法，用于所训练的模型，实现提高识别准确率的目的（主要还是提高自动化识别化石高分辨结构的一种方式）；对于破坏结构的岩石的3D结构的恢复，计算机断层扫描。
古生物图像的计算分析即计算机视觉分析，可用于动植物研究、微生物进化和特定时期生物栖息环境的模拟，生物进化的研究不仅仅只是生物分子方面的分子钟。
另外AI可以为古生物学研究赋能，创造更多的社会应用价值，解决一些经济活动领域的问题，比如石油勘探，为确定勘探点的潜力，并最大限度的减少采油过程中的相关费用。
传承与创新的意义，现在的古生物学家只需要对处理结果进行评估，对提取得到的微化石进行分类。",0.000665660077051,0.2641509433962264,0.2115384615384615,0.2641509433962264,0.0644096649846962,0.220423775361424,0.1657526493072509,0.219482120838471,0.0515591231861686
152005,6,1506,0,6.0,19988.0,6.0,6.0,5.0,4.333333333333333,5.666666666666667,4.666666666666667,6.0,2.6666666666666665,3.0,4.333333333,5.666666667,4.333333333,5.333333333,4.909259259,4.455555556,4.733333333,3.4,5.2,4.9,5.4,0.5,3.8,3.0,4.333333333,4.0,3.666666667,4.333333333,3.0,4.333333333,4.0,3.6,3.0,2.4,3.6,18,12,12,18,6,D,0,0,0,0,1,1,7,8,4,4,5,5,5,4,3,4,4,4,5,4,4,5,5,4,5,4,3,3,5,5,4,3,3,4,4,4,4,4,4,4,4,3,3,4,4,4,4,3,4,4,4,4,4,5,4,4,1,4,2,2,4,2,3,3,4,5,4,1,4,4,7.625,8,4.5,4.0,4.0,3.6,4.0,3.6,4.5,3.75,4.0,4.0,4.0,1.75,4,1,4,4,8.5,8.5,8.0,8.5,8.5,20,1,9.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,0,1,9,5,4,4,5,4,2,4,4,4,5,3,2,2,3,4,9,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,9,4,4,5,3,3,3,4,4,4,4,3,1,3,2,3,0.818181818,8.4,-1.4,9.2855,-2.2855,9.327,9.805,8.738,9.331,9.225,2.2855,林轩宇,2.0,"ai赋能古生物学发展
注：内容为讨论分工生成，同组内容整合成一版，上交三份
一 在人工智能时代可能焕发新生命力的可能原因
古生物学遇冷的原因有很多：
1，需要学习的知识内容复杂，培养时间和周期长
2.就业遇冷，不符合大部分家长预期
3.国内资源不够丰富，学术垄断和学术壁垒存在
根据搜索结果，古生物学的主要研究内容为“保存在地层中的地质历史时期的生物遗体和遗迹化石”，古生物学需要学习的内容十分复杂，包括且不限于生物学、数学、物理学、地质学、信息技术等，而这些可以在ai的帮助下进行更加系统和针对的学习，制定个性化学习计划来保证在有限的时间内达成所需专业知识学习。
古生物学具体的研究目标包括“系统掌握化石的采集、处理、观察，化石及其保存信息的获取与表达（照相、描述与统计），以及地层剖面的测制与描述等的基本技能与方法。”和“掌握该专业所需的数学、物理学、化学、地理学等的基本内容以及必要的信息技术，能够获取、加工和应用古生物学及相关信息。”其中对于古生物学考察内容的识别和整理可以用到目前的ai技术，对于大量的图像进行识别，帮助考察和评判研究，我们可以将这些数据喂给ai，从而帮助我们识别和判断一些古生物，同时可以将研究成果转换为线上数据库，提高研究效率和资源共享。一些信息技术的工作也可以交给ai来进行，节省学习时间和成本。
而在ai存在的同时，古生物学的学科划分可以更加细化，例如古生物学信息管理和古生物自动识别等跨学科跨门类专业出现会拓展对人才的需要，增加就业岗位。
ai的出现解决了大部分古生物学面临的问题，可以使其焕发出新的生机活力。
二 挑战
受到化石文物数量以及保存完整程度上的限制，在数据规模上,现有的古生物训练数据集远远不及主流数据集，在利用大模型进行根据化石图像进行鉴定识别训练时可能面临训练集不够多，导致训练准确度不够高，这是为保证古生物学这门学科带有历史真实性不可避免的挑战。
同时如果模型参数过大，再利用ai大模型对古文物进行复原是可能会出现幻觉现象，从而影响复原的可靠性。
由于古生物学中的人工智能起步较晚，与主流研究在数据集规模上存在大约20年的差距,在算法上存在大约10年的差距，技术上面临巨大挑战。
数据模态数量的快速增长和复杂性使得数据处理变得繁琐且不一致，同时也缺乏明确的基准来评估数据收集和生成以及不同方法在类似任务上的性能。
三 如何利用AI技术助力该学科的传承、创新与社会应用
目前的AI技术在古生物学中主要是应用在自动化过程的改进，古生物学家希望通过构建更大规模的训练数据集与移植前沿算法，获得更高效的古生物学的人工智能模型。分类任务，图像识别、图像分割、预测，定量化犯法、数据驱动的研究模式，生命科学与地球科学共同的趋势。
研究团队需要对数据集进行准备、标注、模型训练，有针对性地研发适合于画师图像分类的算法，用于所训练的模型，实现提高识别准确率的目的（主要还是提高自动化识别化石高分辨结构的一种方式）；对于破坏结构的岩石的3D结构的恢复，计算机断层扫描。
古生物图像的计算分析即计算机视觉分析，可用于动植物研究、微生物进化和特定时期生物栖息环境的模拟，生物进化的研究不仅仅只是生物分子方面的分子钟。
另外AI可以为古生物学研究赋能，创造更多的社会应用价值，解决一些经济活动领域的问题，比如石油勘探，为确定勘探点的潜力，并最大限度的减少采油过程中的相关费用。
传承与创新的意义，现在的古生物学家只需要对处理结果进行评估，对提取得到的微化石进行分类。",0.0080940093139539,0.3181818181818182,0.2093023255813953,0.3181818181818182,0.1114597851994553,0.6084638947725035,0.1882048100233078,0.2342995169082125,0.0830058939096267
152006,7,1507,0,3.0,19988.0,6.0,6.0,3.0,5.0,6.0,5.0,4.333333333333333,4.333333333333333,4.0,4.0,4.666666667,5.0,5.0,4.060185185,4.361111111,4.166666667,4.0,4.4,3.4,4.1,0.25,4.6,4.666666667,4.0,4.8,4.333333333,4.0,4.5,4.0,3.5,3.8,3.75,4.4,4.8,19,15,22,24,7,D,0,0,0,0,1,1,9,9,4,5,5,4,4,5,5,5,5,5,5,3,5,5,4,5,5,5,5,3,5,5,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,1,5,5,5,5,3,2,5,5,5,4,1,5,6,9.0,9,4.5,5.0,4.6,4.8,5.0,5.0,4.5,5.0,5.0,4.0,5.0,3.5,4,1,5,6,6.0,6.5,6.0,5.5,6.5,25,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,0,2,1,1,0,1,6,4,4,4,4,5,4,5,5,5,5,4,3,3,5,4,9,1,1,1,1,1,1,1,1,1,2,1,1,1,1,0,2,0,2,1,1,7,4,4,4,4,5,5,4,5,5,5,4,3,4,5,4,0.681818182,6.1,2.9,6.9855,2.0145,6.827,7.805,6.738,6.331,7.225,2.0145,刘顶,1.0,"AI对历史的创新推动
AI续写历史残本（思维链）
AI总结各历史文献，按需输出（举一反三、多模态学习）
AI解码文字，还原历史场景（多模态学习、反思能力）
AI结合各学科，如天文，地质活动等，协助历史考证，事件时空定位（多模态学习）
AI转换历史形式，协助历史传播（举一反三、反思能力）
AI生成历史游戏，助力用户沉浸式体验（多模态学习）
挑战：在利用AI进行历史研究的过程中，AI生成内容会存在幻觉问题。",7.803770975975005e-12,0.140625,0.1269841269841269,0.140625,0.0107655502392344,0.2288098735750809,0.0823269709944725,0.3170731707317073,0.0140641904075009
152009,8,1508,0,5.0,19990.0,6.0,6.0,2.333333333333333,3.333333333333333,5.0,4.0,5.0,3.0,3.333333333333333,5.0,5.0,3.666666667,5.0,3.959259259,3.755555556,3.533333333,3.2,3.9,4.6,4.4,0.5,3.2,4.0,4.666666667,3.2,3.666666667,4.0,3.0,4.0,4.5,3.4,3.75,3.4,3.6,17,15,17,18,8,D,0,0,0,0,1,1,8,8,4,3,4,4,4,3,4,3,4,4,4,4,4,4,4,4,3,4,3,3,4,3,4,4,3,3,3,4,3,4,4,4,3,4,4,4,4,3,4,4,3,4,3,4,4,4,2,3,4,4,2,2,4,3,3,4,4,4,4,1,3,2,8.0,8,3.666666667,3.8,3.4,3.4,3.8,3.8,3.833333333,3.5,3.75,3.0,3.666666667,2.75,4,1,3,2,5.5,5.0,5.5,5.5,5.5,25,0,7.0,1,1,1,1,1,1,1,1,1,2,1,1,1,1,0,1,1,1,0,1,1,1,1,1,5,4,5,3,3,4,4,3,4,4,3,2,2,3,4,3,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,8,5,4,5,5,3,4,4,4,4,2,2,2,2,3,4,0.818181818,5.4,2.6,6.2855,1.7145,6.327,6.305,6.238,6.331,6.225,1.7145,刘若文,2.0,"学科：考古学
1.文物发掘后，检测与鉴定可用人工智能，扫描文物鉴定年代
2.博物馆可以设置VR全景参观
3.未来的人也可用人工智能亲身体验考古过程，提高关注度
4.考古教学过程也可以用人工智能对学生进行教学实验，模拟实验环境。
5.文物保护的信息真假或者有些保密内容需要对人工智能做一些规范，避免虚假信息流出
6.利用人工智能将所有文物信息整合后可与其他学科联系，更直观的表现出过去与现在方法的同异，也可以更好的在教学和科普上使用。
7.利用AI识图分辩文物真假。",1.5726537224612875e-09,0.1304347826086956,0.0666666666666666,0.1304347826086956,0.0175307391966029,0.3740436749711896,0.1947124898433685,0.3925925925925926,0.0206431123461691
152010,8,1508,0,4.0,19990.0,4.0,4.0,1.0,3.0,4.666666666666667,3.333333333333333,1.0,3.0,3.0,4.0,3.666666667,4.0,5.0,4.418518519,4.511111111,4.066666667,3.4,4.2,3.8,4.7,0.0,4.8,4.333333333,5.0,4.2,3.333333333,4.0,4.5,4.666666667,4.75,3.4,3.5,3.8,4.0,17,14,19,20,8,D,0,0,0,0,1,1,8,7,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,4,4,4,4,4,3,4,4,4,3,4,4,4,3,3,4,7.375,7,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,4.0,4.0,3.75,4,3,3,4,5.0,3.0,4.0,5.5,5.5,23,0,7.0,1,1,1,1,1,1,1,2,0,2,1,1,0,1,0,1,1,1,1,1,1,1,0,2,6,4,4,4,4,3,3,5,4,4,4,4,3,3,3,3,8,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,1,2,1,1,9,5,5,5,5,4,4,5,5,4,5,5,2,3,3,3,0.772727273,4.6,3.4,5.4855,2.5145,5.827,4.305,4.738,6.331,6.225,2.5145,刘瑜瑾,1.0,"领域：生物医药方面，AI的不当使用可能会违背生命伦理，例如进行非法基因编辑和人体实验；或者AI可能设计出不完善的药物，流通到市场造成安全和经济的双重风险。
预防和监测手段：
1、加强伦理审查：建立严格的伦理审查机制，确保AI在医学领域的应用符合伦理标准。
2、技术监管：开发专门的技术监管工具，实时监控AI在生物医学领域的活动，及时发现异常。开发更智能的网络防御系统，利用AI技术进行实时监控和预警。
3、法律法规：完善相关法律法规，明确AI在生物医学领域的应用边界和责任主体。
4、跨领域合作：加强不同领域间的技术合作，形成综合防御体系。
5、教育与培训：提升相关从业人员的AI安全意识和应对能力。
我们可以给AI设计“疫苗”和“特效药”。疫苗就是防范于未然，构建坚固的AI免疫系统，这个系统需要具备天然的防范能力和不断学习的能力，对于有些没能第一次阻止的有害数据，AI需要学习之后加以“记忆”和巩固。而特效药则是在AI已经受到攻击或者做出错误决策时，能及时纠正和监管。",0.0006756966125458,0.2521008403361344,0.2222222222222222,0.2521008403361344,0.0809918632778342,0.4779096904662562,0.4459541440010071,0.75,0.092391304347826
152011,8,1508,0,3.0,19991.0,3.0,3.0,2.0,1.6666666666666667,4.333333333333333,3.6666666666666665,5.666666666666667,3.0,3.0,3.333333333,4.333333333,4.333333333,4.333333333,4.396296296,4.377777778,4.266666667,3.6,3.8,3.6,4.6,0.375,4.0,4.333333333,4.0,4.2,4.0,4.0,5.0,4.666666667,5.0,4.6,4.25,3.6,4.6,23,17,18,23,8,D,0,0,0,0,1,1,5,3,3,4,5,4,4,5,4,4,3,4,4,4,5,5,4,3,4,4,4,3,4,5,4,4,4,5,4,4,5,4,5,5,4,4,4,5,5,4,4,3,4,4,5,4,4,5,4,4,3,4,3,3,4,3,4,4,4,4,4,2,3,3,3.75,3,4.166666667,3.8,4.0,4.2,4.6,4.4,4.166666667,3.75,4.25,4.333333333,4.0,3.0,4,2,3,3,5.5,6.0,5.0,5.5,5.5,23,1,6.0,0,1,1,1,1,1,1,2,0,2,1,1,0,1,0,2,0,1,1,2,0,2,1,2,5,5,3,4,4,4,4,5,4,5,4,3,4,3,3,3,6,0,1,1,1,0,2,1,1,1,1,1,1,1,1,0,2,0,1,1,1,6,5,3,4,4,4,5,4,4,4,5,3,4,3,3,3,0.545454545,5.5,-0.5,6.3855,-1.3855,6.327,7.305,5.738,6.331,6.225,1.3855,刘喆,3.0,我认为考古学是一个冷门绝学，能够借助AI使其焕发新的活力，原因是目前考古学与AI结合可能较少，时间和客观条件的限制使其科学研究和成果转化受到一定限制，公众也了解很少。具体应用方面，比如说通过AI复原文物的发掘现场，让参观者能够化身考古学家，亲身体验文物发现和保护的全过程，用AI复原一些已经遭到损坏的文物，用vr技术使其更加生动，使其重新展现给大众。用AI整合之前的各类信息，利用举一反三能力，简要判断目标文物的年代等，以历史课为主，教师可以在课堂上开展跨学科教学，在博物馆给公众作科普讲解，撰写针对不同年龄人群的讲解词等。,4.259567670039637e-07,0.1176470588235294,0.072289156626506,0.1176470588235294,0.0206446046712269,0.4720276214127799,0.1359921097755432,0.326797385620915,0.0279955207166853
152015,10,1510,0,2.0,19993.0,4.0,5.0,3.0,3.0,4.333333333333333,2.333333333333333,4.0,2.333333333333333,3.333333333333333,3.666666667,3.666666667,4.0,3.666666667,2.608333333,3.65,2.9,2.4,2.5,3.6,3.8,0.5,3.4,3.333333333,3.666666667,3.6,3.0,3.666666667,4.5,4.0,4.5,3.4,3.75,3.4,3.6,17,15,17,18,10,D,0,0,0,0,0,1,6,6,3,4,3,4,4,4,4,3,3,3,3,4,4,3,4,4,3,4,4,3,3,4,4,3,2,2,3,5,5,3,4,4,4,4,3,3,5,2,5,3,3,3,3,3,3,5,4,5,2,4,1,3,4,2,4,4,3,4,4,3,2,5,6.0,6,3.666666667,3.2,3.6,2.8,4.2,3.8,3.666666667,3.25,3.0,4.333333333,4.333333333,2.0,4,3,2,5,7.5,7.0,6.5,7.0,6.5,19,1,6.0,1,2,1,1,1,1,1,1,0,2,1,1,0,1,0,1,1,1,1,1,1,2,1,1,6,4,3,4,3,3,3,4,4,3,3,4,3,3,3,4,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,4,3,4,3,4,3,4,3,3,3,4,2,2,2,3,0.772727273,6.9,-0.9,7.7855,-1.7855,8.327,8.305,7.238,7.831,7.225,1.7855,乔灿宇,2.0,"古文字学科在没有通用人工智能的情况下，入门时间长，对于个人而言需要阅读很多的书。人工智能可以快速阅读大量数据，从而加快古文字翻译。其二，人工智能能更好的找到古文字之间的关联，而普通人会有遗忘现象。同时，人工智能的举一反三，思维链可以很好帮助古文字翻译。但挑战就是，AI仍然有幻觉现象，对于不懂的古文字可能进行胡乱解释。如果资料库少或不准确，会影响AI的翻译。
通过AI的多模态学习，它也能将古文字翻译成趣味性的视频图像，让人更生动了解古文字的起源，了解古文字的魅力。同时建立资料库后，能帮学者更好的检索信息，有利于古文字的学习和探索。总之，使用通用AI能有效帮助古文字传承发展，提升文化自信。",2.5852927884481365e-11,0.0481927710843373,0.0365853658536585,0.0481927710843373,0.0164238416628681,0.3677882755889793,0.1798169314861297,0.4385964912280701,0.0202812330989724
152016,10,1510,0,10.0,19994.0,4.0,4.0,2.6666666666666665,3.333333333333333,4.0,4.666666666666667,5.0,4.0,2.6666666666666665,4.666666667,5.0,4.666666667,5.0,4.230555556,4.383333333,4.3,3.8,4.5,4.1,4.7,0.375,4.4,4.0,3.333333333,4.0,3.666666667,3.666666667,4.0,4.333333333,4.25,2.4,3.0,4.4,3.4,12,12,22,17,10,D,0,0,0,0,0,1,8,8,4,4,5,4,4,5,4,4,4,4,4,4,5,4,5,4,5,4,5,4,4,4,4,5,5,4,4,4,4,5,4,5,4,4,5,4,4,4,5,4,4,5,5,4,4,4,4,5,3,4,4,2,4,4,4,5,4,4,4,4,4,4,8.0,8,4.333333333,4.0,4.2,4.4,4.4,4.2,4.5,4.25,4.5,4.0,4.333333333,3.25,4,4,4,4,7.5,7.5,6.5,7.0,7.0,23,1,8.0,0,1,1,1,1,1,1,1,0,2,1,1,0,2,0,1,1,2,1,1,1,1,1,1,8,4,3,4,4,4,3,4,4,4,4,4,2,3,2,3,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,1,1,8,4,2,4,4,4,4,5,4,4,5,4,2,4,4,4,0.727272727,7.1,0.9,7.9855,0.0145,8.327,8.805,7.238,7.831,7.725,0.0145,卿康,1.0,"冷门绝学：古文字注释
在人工智能时代可能焕发新生命力的可能原因：1、人工智能相比人类阅读范围阅读能力更强，缩短学习入门周期；2、人工智能更可能理解抓住众多古文字之间的相互关联，助力陌生古文字的解释；3人工智能的思维链、举一反三、多模态学习、反思能力都能有助于古文字注释的发展
其在未来发展中面临的可能挑战：人工智能技术可能存在幻觉问题，导致胡乱解释古文字；人工智能需要大量精确的数据来训练，但是古文字的数据来源不是规范统一，数据的准确与否会影响人工智能的效果
利用AI技术助力该学科的传承、创新与社会应用方法：可以利用人工智能的多模态技术解释和宣传古文字，展现传统文化的魅力，也可以帮助新入门者快速了解上手这一行业，帮助有经验的研究者迅速检索相关资料验证猜想；社会应用上可以帮助古文字的古今对比，展示古文字文化的源远流长，提高文化自信，助力中华民族伟大复兴。",2.388107160430153e-13,0.0655737704918032,0.05,0.0655737704918032,0.0127167715851602,0.3046772109591718,0.1324633210897445,0.3623188405797101,0.0142531356898517
152017,10,1510,0,7.0,19994.0,3.0,3.0,2.0,4.666666666666667,6.0,3.0,4.0,2.0,2.333333333333333,5.0,5.333333333,4.0,5.333333333,2.764814815,3.588888889,3.533333333,3.2,3.0,4.1,4.1,0.375,4.0,4.0,3.333333333,3.8,3.666666667,3.666666667,3.5,3.333333333,4.0,2.8,3.75,3.2,3.6,14,15,16,18,10,D,0,0,0,0,0,1,5,4,4,4,4,4,4,4,3,3,3,4,4,4,4,5,4,3,3,4,2,2,4,4,3,3,3,2,2,4,4,4,4,4,3,3,4,2,4,3,3,3,3,3,3,3,3,3,2,3,3,3,2,3,3,3,2,2,3,4,4,2,2,2,4.375,4,4.0,3.4,3.2,2.6,4.0,3.2,3.833333333,3.0,3.0,2.333333333,3.0,2.75,4,2,2,2,7.5,7.0,6.5,7.5,7.5,22,0,8.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,0,1,1,1,1,1,7,4,3,4,4,3,4,4,4,4,3,4,3,2,3,2,8,0,1,1,1,0,1,1,1,0,2,1,1,1,1,1,2,0,1,1,1,6,3,3,4,4,4,4,4,4,4,4,4,3,2,2,2,0.681818182,7.2,-2.2,8.0855,-3.0855,8.327,8.305,7.238,8.331,8.225,3.0855,苏欣欣,3.0,"任务二：【冷门绝学】
“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
古文字考据类对于考究文字的起源和文化文明的发展具有重要意义，但传统学习方式下学习周期长，入门时间长，文献数据因为时间原因存在缺失不足的现象，兼之人脑的记忆容量有限，可能很多时候并不能很有效的帮助，也制约了该学科的发展。利用AI能够有效帮助该学科焕发生命力，其原因、挑战和应用如下：
1.原因：缩短学习周期，能够帮助总结文献、资料的内容，节省时间；同时辅助建立不同资料的数据链接，利用大数据高效而精确的定位古文字所出现的地方并建立网络。
2.挑战：（1）只能辅助解释，而不能够产生全新的知识；（2）可能会出现幻觉现象3.数据来源和精确度的要求高，而本学科自身的数据精确度不够；（3）AI的解释更多是已有研究的整理，精准的释义需要人来确认。
3.创新和社会应用：（1）数字化赋能古文字学的宣传，利用AI制作相关的文字演变视频或者是数字化古文字，都能达到较好的宣传效果。（2）利用AI的思维链、举一反三等帮助建立古文字数据库，实时动态调整文字的来源、释义，甚至沟通不同区域、国家，探寻文字的起源和意思。（3）模拟古文字释义，借助AI大模型的概率模拟，试图破解古文字的奥秘。",9.067931447292256e-06,0.188235294117647,0.1547619047619047,0.188235294117647,0.0330202524214851,0.3596431210615333,0.1960958838462829,0.3182795698924731,0.0309507904698285
152018,11,1511,0,5.0,19995.0,2.0,2.0,5.666666666666667,4.0,2.333333333333333,5.0,3.0,3.6666666666666665,5.0,4.0,5.666666667,5.0,5.666666667,3.873148148,3.238888889,3.433333333,3.6,4.9,5.1,5.9,0.125,3.4,2.0,2.333333333,4.0,4.666666667,3.666666667,4.0,4.0,4.5,3.6,4.5,3.6,4.2,18,18,18,21,11,D,0,0,0,0,0,1,6,7,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,3,3,3,3,3,3,4,3,4,4,3,4,4,3,3,3,4,4,3,3,3,3,3,3,3,3,4,3,3,2,3,4,3,3,3,3,3,3,4,4,3,2,2,6.625,7,4.0,4.0,3.2,3.2,3.8,3.4,3.833333333,3.0,3.0,3.333333333,3.0,3.0,4,3,2,2,6.5,5.0,5.5,6.5,6.5,20,0,9.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,0,2,1,2,1,1,1,2,9,4,3,4,5,4,5,5,5,5,2,3,2,5,5,5,6,0,1,1,2,0,1,0,2,0,2,1,1,1,1,0,1,0,2,0,2,6,3,2,2,1,2,3,3,4,4,2,4,2,4,4,3,0.545454545,6.0,0.0,6.8855,-0.8855,7.327,6.305,6.238,7.331,7.225,0.8855,王晨希,3.0,"冷门学科：文物修复，比如壁画、器物、书画等的修复
可能焕发新生命力的可能原因：
随着经济社会的不断发展，人们精神文化生活越来越丰富，大家更热衷于获得精神层面的富足，更加关注中国传统文化
2、利用AI修复文物可以更清楚地展示细节，如图案、文字等，可以更好地展示文物的原貌
3、相较于人工修复能更好地节省人力、预测更多信息，收集更多的历史数据
4、AI可以根据文物的损坏情况，结合历史资料和修复师的经验，设计出更合理的修复方案。例如，通过机器学习算法，AI可以从大量修复案例中学习，提出最优的修复材料和工艺
5、将AI与高科技修复技术进行结合，可以检测出文物的材料、成分和结构等信息
未来发展中面临的可能挑战：修复过程中可能会出现预测错误，并非完全遵循历史原貌，且数字化修复不一定会被大众认可，传统修复手艺可能会失传",9.485377007282484e-06,0.2253521126760563,0.144927536231884,0.2253521126760563,0.0350908233073838,0.4573496831157506,0.1466145515441894,0.3333333333333333,0.0381720430107527
152019,11,1511,0,6.0,10979.0,1.0,1.0,2.333333333333333,4.666666666666667,3.6666666666666665,4.0,4.333333333333333,4.0,4.666666666666667,5.0,4.666666667,4.333333333,5.0,3.784259259,3.705555556,3.233333333,2.4,4.6,3.1,4.5,0.5,3.6,3.333333333,3.333333333,4.4,4.0,4.0,4.0,4.666666667,4.5,3.8,3.75,4.2,4.4,19,15,21,22,11,D,0,0,0,0,0,1,9,8,5,5,4,4,5,4,4,5,5,4,4,5,5,5,4,5,5,5,5,4,5,5,5,5,5,4,5,5,5,4,4,5,4,5,4,4,5,4,4,3,4,4,4,4,4,3,3,4,4,4,4,3,4,4,3,4,3,4,4,3,4,5,8.375,8,4.5,4.4,4.8,4.8,4.6,4.4,4.833333333,3.75,4.0,3.0,4.0,3.75,4,3,4,5,6.5,5.0,5.5,6.5,6.5,21,1,7.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,0,1,1,1,1,1,8,5,3,4,4,4,4,5,4,5,4,4,4,5,5,4,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,1,1,6,4,2,4,3,3,4,4,4,3,4,3,2,4,4,4,0.818181818,6.0,3.0,6.8855,2.1145,7.327,6.305,6.238,7.331,7.225,2.1145,王连硕,2.0,"选择学科
文物修复，比如壁画，文物，国画修复等
分析热门原因
旅游业发展旺盛，人们精神文化生活越来越丰富，大家更热衷于精神层面的旅游，比如了解历史文化
利用AI修复文物可以更清楚地展示细节，如图案、文字等，可以更好地展示文物的原貌
，相较于人工修复能更好地节省人力、预测更多信息，收集更多的历史数据
AI可以根据文物的损坏情况，结合历史资料和修复师的经验，设计出更合理的修复方案。例如，通过机器学习算法，AI可以从大量修复案例中学习，提出最优的修复材料和工艺
将AI与高科技修复技术进行结合，可以检测出文物的材料、成分和结构等信息
三、面临挑战
修复过程中可能会出现预测错误，并非完全遵循历史原貌，且数字化修复不一定会被大众认可，传统修复手艺可能会失传；在AI修复过程中需要掌握传统修复方式与AI修复的人才，对人才的培养也提出了要求。",7.209567678656044e-06,0.1428571428571428,0.1219512195121951,0.1428571428571428,0.033257112975585,0.3102835344445684,0.1191533878445625,0.3212669683257919,0.0355889724310777
152020,11,1511,0,5.0,10980.0,4.0,4.0,2.0,3.0,3.333333333333333,4.0,4.0,2.6666666666666665,3.333333333333333,4.333333333,3.666666667,3.0,5.333333333,3.763888889,3.583333333,3.5,4.0,3.7,3.9,5.1,0.0,3.8,4.0,4.0,4.0,3.666666667,3.333333333,3.5,3.333333333,4.0,2.6,3.5,3.2,3.0,13,14,16,15,11,D,0,0,0,0,0,1,7,6,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,3,4,3,3,4,3,3,3,4,4,4,4,4,3,3,3,3,4,3,3,3,3,3,3,3,3,2,2,4,4,4,3,3,4,3,3,3,3,3,4,3,3,4,6.375,6,3.666666667,4.0,3.6,3.2,4.0,3.2,3.833333333,3.0,3.0,2.333333333,4.0,3.25,4,3,3,4,6.5,5.0,5.5,6.5,6.5,26,0,6.0,1,1,1,1,1,1,1,2,1,2,0,1,0,1,1,1,1,2,1,2,0,2,1,2,5,4,3,3,4,3,4,4,4,4,4,4,3,4,3,3,9,0,2,1,1,0,2,1,1,1,1,1,1,1,1,1,2,0,2,1,1,7,5,3,4,4,4,4,4,4,4,4,3,3,3,2,3,0.727272727,6.0,1.0,6.8855,0.1145,7.327,6.305,6.238,7.331,7.225,0.1145,王钰,1.0,"选择学科
文物修复，比如壁画，文物，国画修复，数字化展示
分析热门原因
旅游业发展旺盛，人们精神文化生活越来越丰富，大家更热衷于精神层面的旅游，比如了解历史文化
利用AI修复文物可以更清楚地展示细节，如图案、文字等，可以更好地展示文物的原貌
，相较于人工修复能更好地节省人力、预测更多信息，收集更多的历史数据。
AI可以根据文物的损坏情况，结合历史资料和修复师的经验，设计出更合理的修复方案。例如，通过机器学习算法，AI可以从大量修复案例中学习，提出最优的修复材料和工艺。
将AI与高科技修复技术进行结合，可以检测出文物的材料、成分和结构等信息。
三、面临挑战
修复过程中可能会出现一些失误，无法完全遵循历史原貌，且数字化修复不一定会被大众认可，很多人去博物馆更想要看真的文物，可能AI参与大家反而更不喜欢。",3.494088118659202e-14,0.0436681222707423,0.0352422907488986,0.0436681222707423,0.0142876758481047,0.3306282698410063,0.1730705350637436,0.5070422535211268,0.016700170094325
152021,12,1512,0,4.0,10980.0,5.0,6.0,5.333333333333333,5.333333333333333,3.333333333333333,3.0,5.0,3.6666666666666665,3.333333333333333,6.0,5.666666667,4.666666667,4.0,4.798148148,3.788888889,4.733333333,3.4,4.2,4.5,4.7,0.375,4.8,5.0,5.0,4.4,3.0,4.666666667,4.5,4.333333333,4.25,4.4,3.75,3.4,4.8,22,15,17,24,12,D,0,0,0,0,0,1,7,5,5,4,4,5,5,5,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,3,5,5,3,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,3,5,5,5,5,5,5,5,5,5,5,5,5,2,3,3,5.75,5,4.666666667,4.8,5.0,4.2,4.6,5.0,5.0,5.0,5.0,4.0,5.0,5.0,5,2,3,3,6.5,5.5,5.5,5.5,6.0,23,1,10.0,1,1,1,1,1,1,1,1,1,2,1,1,0,1,0,1,0,1,0,1,1,1,1,1,7,5,4,5,4,4,1,3,5,4,5,5,2,3,3,4,10,1,1,1,1,0,1,1,1,0,1,1,1,1,1,0,1,0,1,1,1,8,5,5,5,5,5,5,5,5,4,5,5,1,4,4,3,0.636363636,5.8,1.2,6.6855,0.3145,7.327,6.805,6.238,6.331,6.725,0.3145,文致远,2.0,"任务二
选择的冷门学科：古文字识别
选择原因：
具有一定的研究基础
可以对象形字的书写和演变进行分析
驯化方式：
第一阶段：古文字大部分是象形字体，部分字体就是简易图形的变形和组合，将已有的写作思路进行总结作为数据提供给ai，让ai进行无监督学习。
第二阶段：将认为已定义的古文字提问ai检测准确率，进行人工监督学习。
利用方式：
1.使用ai进行模糊文字的识别与残缺文字记录的修复
2.使用卷积技术对文字进行分离，对不同时间阶段文字进行演推，从古至今分析文字的变化，",1.5808406332225864e-13,0.1008403361344537,0.0854700854700854,0.1008403361344537,0.012577030077635,0.3080522225189275,0.14284548163414,0.4744525547445255,0.0171866737176097
152022,12,1512,0,6.0,10981.0,6.0,7.0,1.6666666666666667,4.333333333333333,3.6666666666666665,4.333333333333333,4.666666666666667,3.6666666666666665,3.6666666666666665,5.666666667,4.666666667,4.666666667,5.333333333,4.547222222,4.283333333,3.7,2.2,5.3,3.5,5.0,0.0,4.2,4.666666667,4.666666667,4.8,4.0,4.0,3.5,4.333333333,2.25,2.4,4.0,4.2,3.2,12,16,21,16,12,D,0,0,0,0,0,1,8,8,3,4,4,5,5,5,3,2,3,1,4,4,5,4,3,5,2,4,4,4,5,2,3,4,4,4,4,4,5,2,4,4,4,4,4,4,4,2,2,3,4,3,3,3,3,3,2,3,4,3,4,3,3,5,2,3,4,3,3,1,4,1,8.0,8,4.333333333,2.6,3.8,3.8,3.8,4.0,3.833333333,2.75,3.0,2.333333333,3.0,4.0,3,1,4,1,8.0,8.0,7.5,8.0,7.5,23,0,8.0,1,2,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,2,1,1,0,1,6,4,5,3,4,4,4,5,4,5,5,5,3,4,3,4,9,1,1,1,1,0,1,1,1,0,1,1,1,1,2,0,2,1,1,1,1,8,5,4,5,4,5,5,5,5,4,4,3,2,3,4,4,0.727272727,7.8,0.2,8.6855,-0.6855,8.827,9.305,8.238,8.831,8.225,0.6855,郗家禾,3.0,"根据我们讨论的笔记，用AI润色优化如下：
一、选择古文字识别学科的依据
学科基础扎实：该领域具有深厚的研究积累和严谨的学术传统，为AI应用提供了可靠的知识基础。
数据特征独特：古文字有一定的象形特征和演变规律
跨学科融合：结合考古学、历史学、语言学等多个领域
二、人工智能助力古文字研究的创新路径
基于深度学习的文字识别 
运用图神经网络进行文字结构分析和部件识别
利用无监督学习发现字形规律和演变模式
通过相似度算法构建古文字关联网络
智能辅助考古实践 
对模糊不清的出土文字进行AI增强识别
建立多模态知识图谱，整合文字、图像与考古信息
实现文物信息的快速数字化与系统分析
文字演变研究的突破 
构建时序演化模型，追踪文字发展脉络
利用AI推演不同时期文字的过渡形态
揭示汉字系统的历史发展规律
三、面临的挑战与应对策略
数据质量问题 
出土文物残缺不全，增加识别难度
需要建立高质量的训练数据集
开发针对性的数据增强技术
准确性验证 
建立专家审核机制，确保AI识别结果的可靠性
开发可解释性模块，提升模型透明度
构建评估标准体系
学科传承与创新 
平衡传统研究方法与AI技术的结合
培养跨学科人才，提升数字素养
促进国际合作，共享研究资源",3.283361524481603e-09,0.09375,0.0793650793650793,0.09375,0.0198381309806497,0.4547345894588624,0.1308546513319015,0.3817567567567567,0.0222487087802939
152023,12,1512,0,3.0,10981.0,2.0,2.0,6.0,4.0,6.0,2.333333333333333,3.0,1.6666666666666667,2.0,6.0,6.0,2.666666667,6.0,3.909259259,3.455555556,3.733333333,1.4,5.5,3.6,5.1,0.0,4.8,5.0,4.0,4.8,3.666666667,2.666666667,3.5,4.333333333,4.75,4.0,4.75,3.2,3.8,20,19,16,19,12,D,0,0,0,0,0,1,5,6,5,4,5,5,4,5,4,5,4,3,5,5,5,5,3,5,5,3,5,3,5,3,4,3,4,3,4,5,3,4,5,3,4,4,3,5,3,4,3,4,4,4,4,3,5,3,4,3,4,3,4,4,4,4,4,3,4,5,3,3,3,2,5.625,6,4.666666667,4.2,3.8,3.6,4.0,3.8,4.666666667,3.75,4.0,3.666666667,3.333333333,4.0,3,3,3,2,9.5,9.0,8.5,9.0,9.5,26,0,4.0,0,1,1,1,1,1,1,1,1,2,1,1,1,1,1,1,0,2,1,1,0,1,0,2,5,3,2,3,4,3,4,5,4,5,5,5,5,2,2,2,4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,5,4,3,5,5,5,5,4,5,5,5,5,3,2,2,1,0.772727273,9.1,-4.1,9.9855,-4.9855,10.327,10.305,9.238,9.831,10.225,4.9855,闫赵桐,1.0,"古文字识别学在人工智能时代新的机遇与潜力
一、焕发新生命力的原因
1. 海量数据处理：通用人工智能具备强大的数据处理能力。古文字学研究涉及大量的古代文献、文物资料等，传统人工整理和分析耗时费力。AI能快速扫描、识别和整理这些资料，例如对甲骨文、金文等古文字的拓片进行数字化处理，通过图像识别技术提取文字信息，建立大型古文字数据库，为研究提供全面的数据支持。
2. 跨领域知识整合：多模态学习使AI能够整合不同类型的信息。古文字学研究并非孤立，它与历史学、考古学、语言学等多学科交叉。AI可综合分析来自不同领域的数据，如结合历史文献记载与考古发现的文物上的古文字，推断文字背后的社会制度、文化习俗等，挖掘出更多有价值的信息。
3. 推理与思维链辅助：AI的思维链能力有助于解决古文字学中的复杂问题。面对古文字的释读难题，AI可以根据已知的文字演变规律、语法结构以及上下文语境，进行逻辑推理和分析，提出合理的释读假设，辅助学者更快地突破研究瓶颈 。
 二、AI的应用方法：
1. 无监督学习：卷积技术分离文字与不同部位 
原理：在处理古文字图像（如甲骨文的龟甲、金文的青铜器铭文拓片）时，卷积神经网络（CNN）能够自动学习图像的特征。通过卷积层的卷积核在图像上滑动，提取局部特征，如笔画的形状、走向等。无监督学习方式下，模型不需要预先标记文字及其部位，而是通过自组织映射等算法，依据图像特征的相似性，将文字区域与背景、装饰图案等不同部位分离出来。
应用于古文字学：这有助于从复杂的文物图像中快速准确地提取古文字，为后续研究提供清晰的文字素材。例如，对于刻有大量铭文的青铜器图像，能自动分离出文字部分，避免人工筛选的繁琐与误差，极大提高资料整理效率，助力古文字学资料的数字化传承。 
2. 相似性检测，文字举一反三 
原理：利用深度学习中的度量学习方法，如孪生网络（Siamese Network）。将古文字图像输入孪生网络的两个分支，经过卷积层、池化层等处理后，得到文字的特征向量。通过计算不同特征向量之间的距离（如余弦距离），判断文字之间的相似性。当发现相似文字时，基于已知文字的解读、用法等信息，进行“举一反三”的推理。例如，若已知某一常见甲骨文的含义和用法，当检测到与之相似的罕见甲骨文时，可推测其可能的含义和用法。
应用于古文字学：在古文字释读中，相似性检测可辅助学者发现文字之间潜在的联系。对于一些尚未释读的古文字，通过与已释读文字的相似性分析，为释读提供新线索，推动古文字学理论的创新。 
3. 人工监督学习：利用已有文字对AI进行训练
原理：将大量已释读、标注准确的古文字数据（包括文字图像、对应的现代文字释义、用法等）作为训练集，采用监督学习算法（如反向传播算法）训练神经网络模型。在训练过程中，模型根据输入的古文字图像预测其对应的释义、用法等，通过与真实标注的对比，计算损失函数，并调整模型参数，使预测结果不断接近真实值。
应用于古文字学：经过人工监督学习训练的AI模型，能够更准确地对新出现的古文字进行解读和分析。例如，在面对新出土文物上的古文字时，可借助训练好的模型快速给出初步的解读建议，提高研究效率，同时为古文字学知识在文物鉴定等社会应用场景中提供技术支持。 
4. AI进行模糊文字识别
原理：针对模糊不清的古文字图像，首先利用图像增强技术，如基于生成对抗网络（GAN）的超分辨率重建方法，对模糊图像进行清晰化处理。然后，将增强后的图像输入到训练好的文字识别模型（如基于CNN的光学字符识别OCR模型）中。模型通过学习大量清晰古文字图像的特征，对模糊文字进行特征匹配和识别，预测出最可能的文字内容。
应用于古文字学：许多出土文物上的古文字因年代久远、保存条件等问题模糊不清，AI的模糊文字识别能力可挽救这些珍贵信息，使更多模糊的古文字得以释读，丰富古文字学研究资料，推动古文字学的传承与发展。
5. 文字演变推演：分析从古至今文字的变化
原理：利用循环神经网络（RNN）及其变体（如长短时记忆网络LSTM）。将不同时期的古文字按照时间顺序作为序列数据输入模型。RNN能够处理序列数据，通过隐藏层保存和传递时间序列中的信息，捕捉文字在不同时期的变化特征。例如，分析不同历史时期汉字的笔画、结构变化规律，从甲骨文到金文、篆书、隶书、楷书等字体的演变。
应用于古文字学：通过AI推演文字演变，可直观展示文字发展脉络，帮助学者深入理解文字演变背后的文化、社会因素。同时，以可视化的方式呈现给大众，开发科普产品，如古文字演变的动画演示，促进古文字学知识在社会中的传播应用，助力古文字学的社会推广与传承。
三、未来发展面临的挑战
1. 数据准确性与可靠性：古文字学数据的来源多样且复杂，存在很多模糊和错误信息。AI系统依赖高质量的数据进行训练，如果输入的数据不准确，可能导致错误的分析结果。如何确保AI所使用的古文字数据经过严格考证和筛选，是一大挑战。
2. 专业知识门槛：古文字学研究需要深厚的专业知识和对古代文化的深刻理解。AI虽然能处理数据，但缺乏对古文字背后文化内涵的直观感受和深入理解。培养既懂古文字学又熟悉AI技术的复合型人才难度较大，限制了AI在古文字学领域的深度应用。
3. 伦理和文化问题：在利用AI进行古文字研究时，可能涉及到对传统文化的解读和阐释权问题。AI生成的结果可能与传统学术观点产生冲突，如何在尊重传统文化和学术规范的基础上，合理运用AI技术，避免对文化的曲解和滥用，是需要谨慎对待的问题。
四、利用AI技术助力学科发展 
1. 传承方面：通过AI技术构建古文字数字化图书馆、虚拟博物馆等，将珍贵的古文字资料永久保存并以生动的形式展示给大众，提高公众对古文字学的认知和兴趣，吸引更多人投身于该领域的学习与研究。
2. 创新方面：借助AI的分析能力，挖掘古文字之间新的联系和演变规律，为古文字学理论创新提供支持。例如，利用机器学习算法分析不同时期、不同地域的古文字特点，发现以往未被注意到的文字发展脉络。
3. 社会应用方面：古文字学与文化创意产业结合，AI可辅助开发古文字相关的游戏、文创产品等。如利用多模态学习，开发能够识别用户书写古文字并进行实时讲解的应用程序，让大众在娱乐中学习古文字知识，促进古文字学的社会传播 。",5.692416483360351e-07,0.1423076923076923,0.1235521235521235,0.1423076923076923,0.0245999953420127,0.2622798190715099,0.1996826380491256,0.3210702341137124,0.0249511021832214
162000,5,1605,启发员,8.0,20951.0,7.0,7.0,5.0,5.0,4.0,3.6666666666666665,2.333333333333333,3.6666666666666665,4.0,5.0,5.0,5.0,5.0,3.994444444,3.966666667,3.8,3.8,4.7,4.1,4.3,0.375,3.4,3.666666667,3.0,4.0,3.0,3.0,4.0,3.333333333,4.0,3.0,4.0,3.6,3.8,15,16,18,19,5,E,1,1,0,0,1,0,6,6,4,4,4,4,4,4,3,3,3,3,4,4,4,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,2,4,3,4,3,3,4,3,3,3,4,4,4,3,4,5,6.0,6,4.0,3.2,4.0,4.0,4.0,4.0,3.5,4.0,4.0,3.0,4.0,3.0,4,3,4,5,3.0,2.0,2.5,2.5,2.5,20,1,6.0,0,1,1,1,1,1,1,1,0,1,1,1,0,1,0,1,0,1,0,1,0,1,1,1,6,4,2,3,3,3,3,4,4,4,4,4,3,4,4,4,6,0,1,1,1,0,1,1,1,0,1,1,1,1,1,0,1,1,1,0,1,6,4,2,3,3,4,4,4,4,4,2,3,3,3,4,4,0.454545455,2.5,3.5,3.3855,2.6145,3.827,3.305,3.238,3.331,3.225,2.6145,李靖洋,1.0,能源动力。根据路况判断行驶时调配发动机的功率，实现时间与能源上的双重节约；基于已有的考古文字进行训练，判断那些未确定的古文字表述的含义,6.191016440512913e-24,0.0,0.0,0.0,0.0041634449533099,0.3672056371360167,-0.0020642310846596,0.3513513513513513,0.0069742489270385
162001,5,1605,协调员,4.0,20952.0,4.0,4.0,4.0,3.6666666666666665,6.0,4.0,2.0,3.6666666666666665,2.6666666666666665,5.333333333,4.333333333,5.333333333,4.0,3.968518519,3.811111111,2.866666667,2.2,3.7,2.9,2.9,0.625,3.8,4.333333333,3.666666667,3.6,3.666666667,3.0,4.5,3.666666667,2.75,3.2,4.0,3.6,3.0,16,16,18,15,5,E,1,2,0,0,1,0,8,1,5,5,5,5,5,5,5,3,5,3,3,5,4,2,4,4,4,4,4,4,4,2,4,2,4,4,3,5,5,5,5,5,5,4,4,4,5,4,4,2,4,4,4,4,4,4,4,4,2,4,1,2,4,2,3,4,4,5,5,3,3,5,3.625,1,5.0,3.8,3.6,3.4,5.0,4.4,3.833333333,3.5,4.0,3.666666667,4.0,1.75,5,3,3,5,3.5,2.5,3.0,3.0,2.5,20,0,6.0,0,1,1,1,0,1,1,2,1,1,1,1,0,1,1,2,1,2,0,2,0,2,1,1,6,4,2,3,4,3,4,3,4,2,5,4,4,2,3,3,6,1,2,1,2,0,2,1,1,0,2,1,2,1,1,1,2,0,2,1,1,5,4,3,4,5,4,4,4,4,3,4,4,3,4,4,3,0.636363636,2.9,5.1,3.7855,4.2145,4.327,3.805,3.738,3.831,3.225,4.2145,徐一冰,3.0,"专业：武术与民族传统体育。
AI智能评分系统：通过摄像头和红外线等体外检测技术捕捉动作，进行评分，避免人工评分存在的主观性和动作漏洞；
编武：根据每个人的肌肉耐受度和年龄，性别等因素进行编排",4.101928859538122e-12,0.064516129032258,0.0,0.064516129032258,0.010789814415192,0.342391654169538,0.0155317280441522,0.375,0.0163934426229508
162002,5,1605,记录员,5.0,20952.0,3.0,3.0,1.0,2.0,6.0,5.333333333333333,2.6666666666666665,3.0,3.0,6.0,3.666666667,6.0,6.0,4.809259259,4.855555556,4.133333333,4.8,4.3,4.4,4.4,0.5,4.0,4.666666667,4.333333333,4.0,4.666666667,4.666666667,4.0,5.0,4.0,1.0,3.25,3.4,3.0,5,13,17,15,5,E,1,3,0,0,1,0,9,9,5,5,5,5,5,5,2,4,3,3,4,4,4,4,3,4,4,5,5,5,5,3,4,4,4,4,4,5,5,5,5,5,4,4,4,4,4,4,4,3,4,4,4,4,4,3,3,5,2,4,2,2,4,2,2,3,4,4,4,1,2,4,9.0,9,5.0,3.2,4.6,4.0,5.0,4.0,3.833333333,3.75,4.0,2.666666667,4.333333333,2.0,4,1,2,4,3.5,2.5,4.5,4.0,4.5,23,0,7.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,1,8,4,5,5,5,4,5,3,4,3,5,5,3,3,3,3,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,8,4,4,5,5,5,4,4,4,4,4,4,3,3,3,3,0.863636364,3.8,5.2,4.6855,4.3145,4.327,3.805,5.238,4.831,5.225,4.3145,吕秋含,2.0,管理科学与工程。多主体协同，在自然灾害发生时，协调政府部门、社会组织和受灾群众之间的资源调配、路径规划等问题。灾害链的推演，基于已发生的灾害及衍生灾害的发生情况，推测灾害链关系。,5.782403931520567e-17,0.0,0.0,0.0,0.0069599951582642,0.4145770460719136,0.0559064224362373,0.3773584905660377,0.0109289617486338
162003,6,1606,启发员,4.0,20953.0,3.0,3.0,3.0,4.333333333333333,4.0,5.0,3.0,4.0,3.0,4.333333333,5.0,4.0,5.0,4.353703704,4.122222222,4.733333333,4.4,4.9,4.0,5.1,0.75,3.8,4.666666667,3.666666667,3.6,3.666666667,2.666666667,3.5,4.0,3.5,4.2,4.5,4.4,4.4,21,18,22,22,6,E,1,1,0,0,1,0,6,7,4,3,4,4,3,3,4,4,4,4,4,3,4,3,4,5,5,4,3,3,4,3,4,4,4,4,4,4,3,4,4,4,3,4,4,4,3,4,4,3,4,4,4,4,4,3,4,4,3,4,4,3,4,4,3,4,4,4,4,3,2,3,6.625,7,3.5,4.0,3.4,4.0,3.8,3.6,4.0,3.75,4.0,3.333333333,4.0,3.5,4,3,2,3,5.5,6.0,5.5,5.5,5.5,22,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,1,8,3,2,3,4,3,4,4,4,4,2,4,2,3,3,3,9,0,1,1,1,1,1,1,1,0,1,1,1,1,1,0,2,1,1,1,1,8,4,3,4,5,5,4,4,5,4,3,3,3,4,4,4,0.727272727,5.6,0.4,6.4855,-0.4855,6.327,7.305,6.238,6.331,6.225,0.4855,亓泽正,3.0,"对于植物保护专业，其实践的关键环节包括植物采样、植物分类、遗传学分析、物种多样性研究等。人工智能技术能够为这些环节提供技术支持，以提高实验效率。
对于植物采样和分类环节，可以采用多模态大模型来进行辅助分类，能够增加区分样本类别的速度，并且提高对难分样本的分类准确性。
对于遗传学分析，可以利用使用领域内专家知识对大模型进行Fine-tuning，以得到更加具有遗传学专业知识的大语言模型，此外还可以针对遗传学的数据结构对模型的多模态能力进行提升。
此外，对于物种多样性研究，还可以借助世界模型的思想，构建一个物种多样性演化世界模型。",0.000319450250353,0.0,0.0,0.0,0.0464285714285714,0.5068269398114412,0.1406442373991012,0.2957746478873239,0.0511811023622047
162004,6,1606,协调员,6.0,20953.0,0.0,0.0,2.0,6.0,4.333333333333333,5.333333333333333,6.0,1.0,1.3333333333333333,5.666666667,4.666666667,4.333333333,6.0,2.961111111,3.766666667,2.6,2.6,4.7,4.6,5.7,0.375,3.4,4.0,1.333333333,4.2,4.333333333,1.333333333,5.0,5.0,5.0,5.0,5.0,5.0,5.0,25,20,25,25,6,E,1,2,0,0,1,0,2,10,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,1,1,7,7.0,10,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,3,1,1,7,4.5,4.5,5.5,5.5,5.5,20,1,3.0,0,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,2,0,1,1,1,3,2,1,1,4,5,4,5,5,5,1,5,1,2,1,1,2,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,1,1,2,2,1,1,4,4,4,5,5,5,1,1,1,1,1,1,0.727272727,5.1,-3.1,5.9855,-3.9855,5.327,5.805,6.238,6.331,6.225,3.9855,翟建欣,2.0,"    经过讨论，我们决定以吴环宇同学所学专业“野生动物与植物保护（植物方向）”为讨论对象。
    人工智能的发展，可能有助于在植物识别以及未发现性状预判方面推进研究。植物识别方面，人工智能可以借助外在设备（比人眼更好的观察设施）同时结合其经过大量专业数据训练的识别库（比人脑更准确专业的识别系统）来达到更好的效果。在未发现性状预判方面，由于人工智能预判蛋白质结构的先例存在，同时基因表达产生不同的蛋白质并且排列组合而后表现出性状，使得人工智能在同一类群中预判未发现新性状成为可能。人难以想象未曾见过的事物，而人工智能则可以从性状表现的根源出发，从基因遗传到蛋白质表达再到性状表现，层层递进，进而进行合理的预测。
    综上，人工智能的发展可以至少在未发现性状预测以及植物识别方面推进野生动物与植物保护（植物方向）的专业研究。",0.0007510496320783,0.0,0.0,0.0,0.0518506700701978,0.3679410660971051,0.1606134921312332,0.2085308056872037,0.0624071322436849
162005,6,1606,记录员,4.0,20954.0,4.0,4.0,1.0,1.0,2.0,4.333333333333333,5.666666666666667,3.0,3.333333333333333,5.0,4.333333333,2.0,2.666666667,3.764814815,4.588888889,3.533333333,3.2,3.4,2.5,4.4,0.125,4.0,3.0,3.666666667,3.8,3.0,3.333333333,3.5,4.333333333,4.5,2.2,2.25,3.2,4.0,11,9,16,20,6,E,1,3,0,0,1,0,6,8,4,3,4,5,2,2,2,4,5,5,2,5,3,4,4,4,3,3,3,3,4,4,3,3,4,2,2,4,4,3,4,4,3,4,4,4,4,3,3,2,2,3,2,4,3,4,4,5,2,5,2,2,5,2,5,4,2,3,3,3,3,1,7.25,8,3.333333333,3.6,3.4,2.8,3.8,3.8,3.833333333,2.5,3.0,4.333333333,5.0,2.0,3,3,3,1,4.0,4.0,4.5,4.5,4.0,23,0,8.0,1,1,0,1,1,1,1,1,0,2,1,1,1,1,0,2,1,2,1,1,0,2,0,1,4,4,3,3,3,3,3,4,4,4,4,3,4,4,3,3,6,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,0,1,6,4,3,4,3,3,3,4,4,4,4,4,2,3,3,3,0.681818182,4.2,1.8,5.0855,0.9145,4.827,5.305,5.238,5.331,4.725,0.9145,吴环宇,1.0,"吴环宇
关于植物物种识别，使用智能AI大模型训练识别物种的显著形态特征，增强对每个物种的形态（叶，花，果实等）的知识储备，增加在不同场景，不同时间的植物特征的图片，以及排除其他植物作为背景的干扰，结合采集时间，采集地点（海拔等），往年标本等综合识别，并利用反馈进行增强。",3.976539890501658e-12,0.074074074074074,0.0,0.074074074074074,0.0103662750518313,0.4967795496513194,0.0465899147093296,0.3452380952380952,0.0151041666666666
162006,7,1607,启发员,4.0,20954.0,2.0,2.0,5.0,3.333333333333333,3.0,4.666666666666667,5.333333333333333,3.333333333333333,3.333333333333333,5.0,4.0,5.0,5.0,3.175925926,4.055555556,3.333333333,3.0,3.2,3.3,3.8,0.25,4.2,4.0,3.333333333,4.0,3.666666667,3.0,4.0,4.0,4.25,3.4,4.25,3.6,3.6,17,17,18,18,7,E,1,1,0,0,1,0,8,8,4,4,4,4,4,4,3,3,3,3,5,4,4,4,4,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,4,5,4,4,4,4,4,4,4,5,4,4,4,2,2,4,2,3,2,2,3,3,2,3,4,4,5,3,3,5,8.0,8,4.0,3.4,4.0,4.0,4.2,4.2,4.333333333,4.0,4.25,2.0,3.333333333,2.25,5,3,3,5,3.0,2.5,2.5,2.5,2.0,23,1,8.0,1,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,0,2,1,1,1,1,1,1,5,4,2,3,4,4,3,5,4,4,4,3,2,3,3,4,9,1,1,1,1,0,1,1,1,1,1,1,1,0,2,1,1,0,1,1,1,8,4,2,4,4,4,4,5,4,4,4,4,2,3,3,4,0.727272727,2.5,5.5,3.3855,4.6145,3.827,3.805,3.238,3.331,2.725,4.6145,谢善杰,2.0,"甲骨文分析：使用AI直接将甲骨文这类只有文字，没有其具体含义的数据，投给AI大模型，让AI进行学习其中的关系，进行含义的解释，甚至生成创作其他的甲骨文。
电影解读：投喂AI一部电影，生成电影的解说视频，有一定的商业价值。",1.368488308130684e-11,0.1568627450980392,0.1224489795918367,0.1568627450980392,0.0125113739763421,0.5453465988431909,0.0390189588069915,0.4090909090909091,0.017832647462277
162007,7,1607,协调员,6.0,20955.0,6.0,7.0,4.0,4.666666666666667,5.0,5.0,3.333333333333333,5.0,4.666666666666667,4.666666667,5.0,5.0,5.0,4.228703704,4.372222222,4.233333333,4.4,4.8,3.9,3.9,0.0,5.0,5.0,5.0,5.0,5.0,5.0,3.0,5.0,5.0,4.0,4.75,5.0,5.0,20,19,25,25,7,E,1,2,0,0,1,0,10,10,5,5,5,5,5,5,5,2,3,3,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,5,5,5,5,5,5,5,5,4,5,4,5,5,5,5,5,3,4,4,2,4,2,2,4,1,4,4,4,5,5,4,5,4,10.0,10,5.0,3.6,5.0,5.0,4.8,5.0,5.0,4.5,5.0,3.666666667,4.0,1.75,5,4,5,4,2.5,2.0,2.0,2.5,2.0,27,1,9.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,9,5,5,5,5,5,5,5,5,5,5,5,3,4,5,5,10,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,9,5,5,5,5,5,5,5,5,5,5,5,2,5,5,5,0.772727273,2.2,7.8,3.0855,6.9145,3.327,3.305,2.738,3.331,2.725,6.9145,吴曦,1.0,"AI 用于历史语言的学习和翻译，例如考古文的学习和理解，以及转换为现在常用的语言（中文和英文）。这样能够很好的帮助你们解决语言的理解问题，以及发现历史语言中承载的故事和秘密。
AI 模仿历史大家的文学风格或者作曲风格，重新把现代化的故事和歌曲，以历史人物大家的风格重新展示出来，让人们从身边的故事感受到历史人物的魅力。
AI 整理电影故事以及流行电影风格，融合彼此，创作出新的电影供人们观影。AI的出现能够减少演员的参与以及提高电影情节的多样性，可以按照观众的喜好设计出特定的剧情。",5.5481757177512794e-09,0.1176470588235294,0.0909090909090909,0.1176470588235294,0.0171514818880351,0.4982170130703016,0.1358681321144104,0.3877551020408163,0.0236220472440944
162008,7,1607,记录员,2.0,20955.0,2.0,2.0,2.6666666666666665,5.0,6.0,4.666666666666667,3.0,3.0,3.333333333333333,6.0,6.0,5.0,5.0,3.890740741,3.344444444,3.066666667,3.4,4.6,4.2,4.4,0.875,4.2,4.666666667,4.333333333,4.0,4.0,3.666666667,4.0,4.666666667,4.75,3.4,4.0,3.8,3.6,17,16,19,18,7,E,1,3,0,0,1,0,8,9,4,3,4,4,3,5,3,3,3,3,4,3,5,5,3,4,4,5,4,3,4,5,4,3,4,4,4,4,4,4,5,5,5,3,4,4,4,3,4,4,5,4,4,4,4,3,4,4,1,4,1,1,4,1,3,2,5,5,5,4,3,4,8.625,9,3.833333333,3.2,4.2,3.8,4.4,4.0,4.0,4.0,4.0,3.333333333,4.0,1.0,5,4,3,4,2.0,2.0,2.5,2.5,2.0,21,1,7.0,1,1,1,1,1,2,1,1,1,2,0,1,0,1,0,1,1,2,0,2,0,2,1,2,7,4,4,3,4,4,4,4,4,5,3,4,3,3,3,4,8,1,1,1,1,0,2,1,1,1,1,1,1,0,1,0,2,1,2,1,1,8,5,4,4,4,5,5,4,4,5,4,4,1,3,3,3,0.636363636,2.2,5.8,3.0855,4.9145,2.827,3.305,3.238,3.331,2.725,4.9145,武轩宇,3.0,"冷门绝学-7组-武轩宇
AI如何结合？
“考古：甲骨文”人们虽然也不理解甲骨文的意义，但AI学习后可以翻译为中文，帮助我们理解。
预测蛋白质的折叠后的空间结构，计算一些研究的可行性。
AI配音、编曲、唱歌，降低了短视频创作门槛",2.3624649312776798e-07,0.1935483870967741,0.1379310344827586,0.1935483870967741,0.0195694716242661,0.4362790264653984,0.0551108457148075,0.3333333333333333,0.0272511848341232
162009,9,1609,启发员,4.0,20956.0,3.0,3.0,3.333333333333333,5.333333333333333,3.6666666666666665,4.0,6.0,2.333333333333333,2.0,3.666666667,4.333333333,4.333333333,4.0,4.175,4.05,3.3,2.8,4.2,3.5,4.8,0.125,4.4,3.0,3.333333333,5.0,3.666666667,4.0,3.0,4.333333333,4.75,2.8,3.25,2.6,4.2,14,13,13,21,9,E,1,1,0,0,0,0,9,9,5,4,4,5,5,4,5,5,3,4,5,5,4,2,5,5,4,4,4,4,2,3,3,3,3,4,3,4,4,2,5,5,4,4,4,4,5,4,4,3,4,4,4,3,3,4,3,3,3,4,2,2,4,2,2,4,4,4,4,2,3,3,9.0,9,4.5,4.4,3.4,3.2,4.0,4.2,4.166666667,3.75,3.5,3.0,3.666666667,2.25,4,2,3,3,6.0,6.5,5.5,6.0,6.0,19,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,1,1,2,8,5,2,5,4,4,3,5,5,5,5,5,3,2,2,2,9,1,1,1,1,1,1,1,1,1,2,1,1,1,1,0,2,1,2,1,1,8,5,1,4,3,3,3,5,5,5,4,3,2,2,2,3,0.909090909,6.0,3.0,6.8855,2.1145,6.827,7.805,6.238,6.831,6.725,2.1145,周敬轩,1.0,"古生物学是一门有发展潜力的学科。
我们可以通过AI多模态学习的能力来向其同时输入数值与文字数据（如分子量、放射性、地层数据等）和图像数据（化石图片、显微观察图片、地层剖面等），来让AI对古生物进行分析和复原，以达到推测古生物年代、所属分类细目等目的。
也可以通过其总结和举一反三的能力，同时收集现存生物的各项生理和行为数据，通过对古生物结构、年代的总结分析来推测出古生物的可能外表和行为特征。还能进一步为其制作精细化的模型，模拟当时的生态系统。
另外，还可以通过以上的结果，生产一些面向大众的科普材料，如动画、3D建模、博物画等等。",4.434572304685676e-10,0.0434782608695652,0.0222222222222222,0.0434782608695652,0.0180799132164165,0.5493066719837856,0.1665658801794052,0.38125,0.0196399345335515
162010,9,1609,协调员,5.0,20956.0,4.0,4.0,4.333333333333333,4.0,4.666666666666667,4.333333333333333,3.6666666666666665,3.0,3.0,6.0,4.333333333,5.0,5.0,3.861111111,4.166666667,4.0,4.0,5.0,4.3,5.0,0.5,4.0,5.0,3.666666667,4.8,5.0,3.666666667,4.5,4.0,4.25,3.8,4.25,3.2,3.8,19,17,16,19,9,E,1,2,0,0,0,0,8,6,4,4,5,5,5,5,5,5,5,5,4,4,4,5,4,4,5,5,5,5,5,5,4,3,3,4,4,4,4,4,4,4,4,3,4,5,5,5,5,4,4,5,4,4,4,4,4,4,2,4,2,2,4,2,4,4,5,5,5,3,4,6,6.75,6,4.666666667,4.8,5.0,3.6,4.0,4.2,4.333333333,4.5,4.25,4.0,4.0,2.0,5,3,4,6,7.5,6.5,6.0,7.0,7.0,20,0,9.0,1,1,1,1,1,1,1,1,1,2,1,2,1,2,0,2,1,1,0,2,1,2,1,2,7,4,3,4,5,5,5,5,5,5,4,5,3,3,3,3,8,1,2,1,1,1,1,1,2,1,2,1,1,1,1,0,2,1,2,0,2,7,4,3,4,5,5,5,4,4,4,4,4,3,3,3,3,0.818181818,6.8,1.2,7.6855,0.3145,8.327,7.805,6.738,7.831,7.725,0.3145,何思扬,2.0,"“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。
·请选择一门你认为具有更大发展潜力的冷门学科：
古生物学 
·分析其在人工智能时代可能焕发新生命力的可能原因，
1标本处理与安全：借助人工智能可高效采集、处理古生物标本信息，结合区块链技术，能保障标本数据安全，为研究奠定基础。
2数据挖掘与比对：通用人工智能强大的搜索和比对能力，能处理海量古生物数据，挖掘规律，助力精准识别物种、推断习性，推动研究进展。
3基于已有数据发掘预测：依据现有数据，利用人工智能算法模型预测古生物结构与生存环境，为实地发掘提供指导，提高发掘效率，促进研究模式转变。
4科普创新，促进教育事业发展：人工智能的多模态学习能力融合文本、图像等信息，生成可视化古生物 3D 模型，既有助于科研，也能以直观形式用于科普，提升大众兴趣。可以应用于博物馆科普等等
·在未来发展中面临的可能挑战。
1 通用智能数据不够专精于特定专业：通用智能数据广泛但针对古生物学的专业性不足，难以满足特定数据要求，影响分析准确性。
2 资金不足：古生物学研究本就资金耗费大，引入人工智能技术后，软件、硬件、人才培养等方面的资金需求增加，学科冷门使其难以吸引足够投入。
3 人机协调问题，专家与人工智能的意见不一等等",9.317672004679784e-05,0.2857142857142857,0.1702127659574468,0.2448979591836734,0.0493577577161118,0.6410074720303718,0.1757684350013733,0.302139037433155,0.0445912469033856
162012,10,1610,启发员,8.0,20957.0,4.0,4.0,4.333333333333333,5.333333333333333,5.0,4.666666666666667,3.0,5.0,3.6666666666666665,5.0,5.666666667,4.666666667,5.0,3.125,3.75,3.5,4.0,4.7,4.0,4.7,0.125,3.6,5.0,4.333333333,1.8,2.333333333,2.0,4.0,4.666666667,3.25,3.4,3.25,4.8,4.8,17,13,24,24,10,E,1,1,0,0,0,0,8,10,5,5,3,4,3,3,3,2,4,2,4,5,3,3,4,4,5,5,4,4,5,3,3,2,3,4,3,5,4,4,4,4,5,5,4,4,4,5,5,4,4,4,4,5,4,2,3,3,4,4,2,4,5,1,3,4,5,5,4,2,3,7,9.25,10,3.833333333,3.0,4.2,3.0,4.2,4.4,4.0,4.5,4.25,2.666666667,4.0,2.75,4,2,3,7,2.0,2.0,2.0,2.5,2.0,20,1,4.0,1,1,1,1,1,2,1,2,1,2,0,2,0,1,1,2,0,2,1,2,0,2,0,2,2,2,2,2,3,1,3,1,1,4,2,1,3,4,3,4,8,1,1,1,1,1,1,1,1,1,2,1,2,1,1,1,2,0,2,0,1,7,5,3,5,5,5,5,4,3,4,4,3,3,5,5,5,0.681818182,2.1,5.9,2.9855,5.0145,2.827,3.305,2.738,3.331,2.725,5.0145,马越林,1.0,人工模拟古代建筑，以玩家的身份体验考古发掘的过程。 读取大量古代文献资料以及现代物理化学相关的信息，以计算机模拟出古代乐器的结构以及他们所使用的材料。,8.625813855474807e-41,0.0,0.0,0.0,0.0023300608922579,0.2590029648650043,0.012866236269474,0.35,0.0039193729003359
162013,10,1610,协调员,8.0,20958.0,5.0,5.0,2.6666666666666665,4.0,4.0,5.666666666666667,3.333333333333333,2.333333333333333,3.0,5.333333333,4.666666667,5.333333333,4.666666667,4.361111111,4.166666667,4.0,4.0,4.1,4.8,4.7,0.25,3.4,4.0,3.0,3.0,3.666666667,2.666666667,4.0,4.0,4.0,3.8,3.75,3.8,3.8,19,15,19,19,10,E,1,2,0,0,0,0,8,9,4,4,4,4,4,4,4,4,4,4,5,4,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,5,4,4,4,4,4,4,4,4,5,4,4,4,5,4,5,4,5,4,4,1,4,2,4,4,1,4,4,4,4,5,4,4,6,8.625,9,4.0,4.2,4.0,4.0,4.2,4.0,4.5,4.25,4.5,4.333333333,4.0,2.0,5,4,4,6,6.0,6.0,6.0,6.0,6.5,20,0,5.0,0,2,1,1,1,1,1,2,0,2,1,2,0,2,0,2,0,2,1,2,1,2,1,2,4,3,2,3,4,3,4,3,4,3,2,3,4,3,2,4,7,1,2,1,2,0,2,0,2,0,2,1,2,1,2,1,2,0,2,0,2,4,4,2,3,4,4,4,4,3,3,3,4,4,3,2,2,0.545454545,6.1,1.9,6.9855,1.0145,6.827,7.305,6.738,6.831,7.225,1.0145,程雯,3.0,"任务二
任务二：【冷门绝学】
“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。
请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
①在人工智能时代可能焕发新生命力的可能原因：
考古学研究中积累了大量的数据，包括遗址发掘记录、文物照片、陶片拓片等。传统方法处理这些数据耗时耗力，而AI技术可以快速处理和分析海量数据，提高研究效率；
AI的图像识别技术可以自动识别和分类文物，减少人工操作的误差和时间成本。例如，山东大学文化遗产研究院的方辉教授通过陶片AI拼对实验，展示了AI在提升拼对效率中的重要性；
②如何利用AI助力该学科：
通过线上建立古建筑、著名景点的模型，让更多人可以在线上平台体验考古的过程和乐趣；
通过AI技术和古代乐器相结合建造线上乐器演奏平台，让人们可以体会到古代乐器演奏的美妙和乐趣；
利用AI的图像识别技术，可以自动识别和分类文物",,,,,,,,,
162014,10,1610,记录员,11.0,20958.0,6.0,7.0,1.3333333333333333,4.333333333333333,4.666666666666667,4.666666666666667,6.0,1.3333333333333333,2.0,3.666666667,3.0,3.333333333,5.0,3.803703704,3.822222222,2.933333333,3.6,4.1,4.8,5.0,0.125,4.0,4.0,3.0,4.4,4.333333333,3.0,3.0,4.0,2.75,1.2,2.5,2.8,2.6,6,10,14,13,10,E,1,3,0,0,0,0,5,5,4,4,4,4,3,3,4,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,4,4,4,4,4,3,3,4,3,4,2,3,4,2,3,3,4,4,4,1,2,4,5.0,5,3.666666667,3.4,4.0,3.6,4.0,4.0,4.0,3.5,4.0,3.0,4.0,2.5,4,1,2,4,4.0,3.0,4.5,5.0,5.0,21,0,5.0,1,1,1,1,1,1,1,1,0,2,1,1,1,1,0,1,1,1,0,2,0,2,1,1,6,3,3,3,4,4,5,5,5,5,3,4,4,2,2,2,5,1,1,1,1,1,2,1,1,0,2,1,1,1,1,1,2,0,2,1,1,7,3,3,3,4,4,4,4,4,5,3,4,4,2,1,1,0.727272727,4.3,0.7,5.1855,-0.1855,4.827,4.305,5.238,5.831,5.725,0.1855,张扬,2.0,"冷门绝学
考古学，遗址研究
通过模型搭建的游戏或技术重新搭建古建筑或遗址群，开放给游客参观。
通过模拟考古过程，使游客充分体验考古过程的乐趣，学习考古相关的知识。
通过AI模拟古代乐器，现代人也可以通过AI技术体验古代乐器，并使用古代乐器进行自主编曲。
通过AI预测古代典籍、古代物品、古代乐器的形态，解读、破解现代人可能需要很多年的研究才能发现的未解之谜。
通过AI技术帮助考古过程，为考古工作人员提供更多的技术支持。",7.430188216399957e-10,0.163265306122449,0.1276595744680851,0.163265306122449,0.0173706333386843,0.4757547105706701,0.141531765460968,0.3833333333333333,0.0211466165413534
162015,11,1611,启发员,9.0,20959.0,6.0,7.0,5.333333333333333,4.333333333333333,6.0,4.666666666666667,1.0,3.0,2.333333333333333,5.333333333,4.333333333,4.333333333,5.666666667,4.413888889,4.483333333,3.9,3.4,4.5,3.8,4.2,0.5,4.4,5.0,4.333333333,4.0,5.0,4.0,4.5,5.0,5.0,1.8,4.25,3.4,3.6,9,17,17,18,11,E,1,1,0,0,0,0,9,6,4,4,4,4,4,3,3,3,3,3,3,4,4,4,5,5,4,5,3,2,2,2,3,2,2,3,2,4,4,3,5,5,4,3,4,4,5,4,4,4,4,5,5,4,4,3,3,4,1,4,1,1,4,1,4,4,5,5,4,3,3,5,7.125,6,3.833333333,3.0,2.8,2.4,4.2,4.0,4.333333333,4.0,4.5,3.333333333,4.0,1.0,4,3,3,5,4.5,4.5,4.5,4.5,6.0,23,0,9.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,1,9,5,2,5,5,5,5,5,4,5,3,3,2,2,2,3,9,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,1,1,1,9,5,3,5,5,5,5,4,5,4,5,4,2,2,2,5,0.954545455,4.8,4.2,5.6855,3.3145,5.327,5.805,5.238,5.331,6.725,3.3145,王涵,2.0,"冷门学科：地质勘查。
冷门原因：采集地质数据的过程较艰苦，工作量大，且采到的数据不一定是有效的。
通用人工智能如何帮助此学科焕发生命力：使用大量采集难度低的地址数据训练大模型，由大模型协助确定进一步精细采集数据的位置等。对预训练好的通用机器人进行微调，教他使用地质勘测工具去野外采集数据，从而代替人类进行又脏又累还技术含量不高的数据收集工作。这样，更多的地质研究者可以聚焦于数据分析，为生产生活提供更多指导。",5.802904634728949e-11,0.0,0.0,0.0,0.0132148553201184,0.4313196513240647,0.1134163811802864,0.3333333333333333,0.0164948453608247
162016,11,1611,协调员,2.0,20960.0,3.0,4.0,4.0,3.6666666666666665,3.0,3.333333333333333,3.6666666666666665,3.6666666666666665,3.6666666666666665,3.666666667,3.333333333,3.0,3.333333333,3.509259259,3.055555556,3.333333333,3.0,3.1,3.6,3.3,0.0,4.0,3.666666667,3.666666667,4.0,4.0,3.666666667,5.0,4.666666667,4.25,4.4,4.5,4.6,4.0,22,18,23,20,11,E,1,2,0,0,0,0,9,9,4,4,5,5,4,5,4,4,4,5,4,5,5,4,4,5,4,4,5,4,4,5,5,5,4,4,4,4,4,4,4,5,5,5,5,5,5,4,4,5,5,5,5,4,4,5,5,4,4,5,5,5,4,5,5,4,4,5,4,4,4,5,9.0,9,4.5,4.2,4.4,4.4,4.2,5.0,4.5,4.5,4.5,5.0,4.333333333,4.75,4,4,4,5,7.0,8.0,7.5,7.0,7.0,25,0,7.0,1,1,1,1,1,2,1,1,1,2,0,1,1,1,1,1,0,1,0,1,0,2,1,1,6,3,4,4,4,4,4,4,4,4,4,4,3,3,4,4,8,1,1,1,1,1,1,1,1,1,2,0,1,0,2,0,2,0,1,0,1,6,4,3,4,4,3,4,4,4,4,4,4,3,4,3,4,0.590909091,7.3,1.7,8.1855,0.8145,7.827,9.305,8.238,7.831,7.725,0.8145,许艺炜,3.0,,,,,,,,,,
162017,11,1611,记录员,8.0,20958.0,5.0,5.0,2.333333333333333,3.333333333333333,5.0,4.666666666666667,4.0,4.0,4.0,4.333333333,4.333333333,4.333333333,4.333333333,4.119444444,4.716666667,4.3,4.8,3.8,2.5,4.2,0.5,4.4,4.0,4.666666667,4.4,3.666666667,4.666666667,3.5,4.333333333,3.75,3.4,3.0,2.6,3.2,17,12,13,16,11,E,1,3,0,0,0,0,4,8,2,2,3,3,4,4,3,4,2,4,2,4,5,5,5,2,3,4,3,4,5,3,5,4,4,3,2,4,4,4,5,4,4,5,5,5,5,4,5,5,3,3,3,4,4,4,5,4,1,4,4,3,5,2,5,5,4,4,2,4,1,2,6.5,8,3.0,3.0,3.8,3.6,4.2,4.8,4.0,4.25,3.5,4.666666667,4.333333333,2.5,2,4,1,2,4.0,3.0,4.5,5.0,6.0,19,0,9.0,1,1,1,1,1,1,1,1,1,2,1,1,1,1,0,1,1,1,1,2,1,2,1,2,7,5,5,4,4,3,4,4,4,5,5,4,4,4,4,4,8,0,2,1,1,0,1,1,1,0,2,1,1,1,1,0,2,0,2,1,1,7,5,5,4,4,4,4,5,4,4,4,5,4,4,4,4,0.727272727,4.5,-0.5,5.3855,-1.3855,4.827,4.305,5.238,5.831,6.725,1.3855,江琳钰,1.0,"地质考察
重要作用
主要工作集中于资源勘查、工程勘察、地质灾害防治等领域（地震监测，地铁选址等），在地球科学研究、资源勘探、环境保护等方面有着重要作用，近年随着地质学技术发展，重要性逐渐上升
面临的问题（冷门原因）
工作环境艰苦且外耗费大量时间精力，
采集难度高，需要花费大量时间采集和研究样本，
并且本身的薪资水平不高，付出回报不成正比
AI技术助力
通用人工智能可以极大程度上的减少采集资源所需的时间成本和人力成本，全方面提升降低研究难度，同时提高研究速度，助力地质学发展",5.114141771194988e-09,0.0298507462686567,0.0,0.0298507462686567,0.0162928174067892,0.436355637016197,0.0673135071992874,0.3088235294117647,0.0182450043440486
